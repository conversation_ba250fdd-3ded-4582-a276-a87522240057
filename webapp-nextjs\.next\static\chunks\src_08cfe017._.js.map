{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-background text-foreground\",\n        destructive:\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Alert = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\n>(({ className, variant, ...props }, ref) => (\n  <div\n    ref={ref}\n    role=\"alert\"\n    className={cn(alertVariants({ variant }), className)}\n    {...props}\n  />\n))\nAlert.displayName = \"Alert\"\n\nconst AlertTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h5\n    ref={ref}\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nAlertTitle.displayName = \"AlertTitle\"\n\nconst AlertDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\n    {...props}\n  />\n))\nAlertDescription.displayName = \"AlertDescription\"\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,6JACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBACnC,6LAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 199, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;KAbS;AAeT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 337, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 389, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { CheckIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Checkbox({\n  className,\n  ...props\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\n  return (\n    <CheckboxPrimitive.Root\n      data-slot=\"checkbox\"\n      className={cn(\n        \"peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <CheckboxPrimitive.Indicator\n        data-slot=\"checkbox-indicator\"\n        className=\"flex items-center justify-center text-current transition-none\"\n      >\n        <CheckIcon className=\"size-3.5\" />\n      </CheckboxPrimitive.Indicator>\n    </CheckboxPrimitive.Root>\n  )\n}\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,6LAAC,uKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+eACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;sBAEV,cAAA,6LAAC,2MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI7B;KArBS", "debugId": null}}, {"offset": {"line": 440, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["'use client'\n\nimport * as React from 'react'\nimport { cn } from '@/lib/utils'\n\nconst DropdownMenu = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & {\n    children: React.ReactNode\n  }\n>(({ className, children, ...props }, ref) => {\n  const [open, setOpen] = React.useState(false)\n\n  return (\n    <div ref={ref} className={cn('relative inline-block text-left', className)} {...props}>\n      {React.Children.map(children, (child) => {\n        if (React.isValidElement(child)) {\n          if (child.type === DropdownMenuTrigger) {\n            return React.cloneElement(child, { onClick: () => setOpen(!open) })\n          }\n          if (child.type === DropdownMenuContent) {\n            return React.cloneElement(child, { open, onClose: () => setOpen(false) })\n          }\n        }\n        return child\n      })}\n    </div>\n  )\n})\nDropdownMenu.displayName = 'DropdownMenu'\n\nconst DropdownMenuTrigger = React.forwardRef<\n  HTMLButtonElement,\n  React.ButtonHTMLAttributes<HTMLButtonElement>\n>(({ className, children, ...props }, ref) => (\n  <button\n    ref={ref}\n    className={cn(\n      'inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n      className\n    )}\n    {...props}\n  >\n    {children}\n  </button>\n))\nDropdownMenuTrigger.displayName = 'DropdownMenuTrigger'\n\nconst DropdownMenuContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & {\n    open?: boolean\n    onClose?: () => void\n    align?: 'start' | 'center' | 'end'\n  }\n>(({ className, children, open, onClose, align = 'start', ...props }, ref) => {\n  const contentRef = React.useRef<HTMLDivElement>(null)\n\n  React.useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (contentRef.current && !contentRef.current.contains(event.target as Node)) {\n        onClose?.()\n      }\n    }\n\n    if (open) {\n      document.addEventListener('mousedown', handleClickOutside)\n      return () => document.removeEventListener('mousedown', handleClickOutside)\n    }\n  }, [open, onClose])\n\n  if (!open) return null\n\n  const alignmentClasses = {\n    start: 'left-0',\n    center: 'left-1/2 transform -translate-x-1/2',\n    end: 'right-0'\n  }\n\n  return (\n    <div\n      ref={contentRef}\n      className={cn(\n        'absolute z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md',\n        'data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',\n        alignmentClasses[align],\n        'top-full mt-1',\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n})\nDropdownMenuContent.displayName = 'DropdownMenuContent'\n\nconst DropdownMenuItem = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      'relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\n      'hover:bg-accent hover:text-accent-foreground cursor-pointer',\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuItem.displayName = 'DropdownMenuItem'\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & {\n    checked?: boolean\n  }\n>(({ className, children, checked, ...props }, ref) => (\n  <DropdownMenuItem\n    ref={ref}\n    className={cn('relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50', className)}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      {checked && (\n        <svg\n          className=\"h-4 w-4\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n        >\n          <path\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n            strokeWidth={2}\n            d=\"M5 13l4 4L19 7\"\n          />\n        </svg>\n      )}\n    </span>\n    {children}\n  </DropdownMenuItem>\n))\nDropdownMenuCheckboxItem.displayName = 'DropdownMenuCheckboxItem'\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & {\n    value: string\n  }\n>(({ className, children, ...props }, ref) => (\n  <DropdownMenuItem\n    ref={ref}\n    className={cn('relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50', className)}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <svg className=\"h-2 w-2 fill-current\" viewBox=\"0 0 8 8\">\n        <circle cx={4} cy={4} r={2} />\n      </svg>\n    </span>\n    {children}\n  </DropdownMenuItem>\n))\nDropdownMenuRadioItem.displayName = 'DropdownMenuRadioItem'\n\nconst DropdownMenuLabel = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      'px-2 py-1.5 text-sm font-semibold',\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuLabel.displayName = 'DropdownMenuLabel'\n\nconst DropdownMenuSeparator = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('-mx-1 my-1 h-px bg-muted', className)}\n    {...props}\n  />\n))\nDropdownMenuSeparator.displayName = 'DropdownMenuSeparator'\n\nconst DropdownMenuShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn('ml-auto text-xs tracking-widest opacity-60', className)}\n      {...props}\n    />\n  )\n}\nDropdownMenuShortcut.displayName = 'DropdownMenuShortcut'\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAEA;AACA;;;AAHA;;;AAKA,MAAM,6BAAe,GAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,UAKlC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;;IACpC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IAEvC,qBACE,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAAa,GAAG,KAAK;kBAClF,6JAAA,CAAA,WAAc,CAAC,GAAG,CAAC,UAAU,CAAC;YAC7B,kBAAI,CAAA,GAAA,6JAAA,CAAA,iBAAoB,AAAD,EAAE,QAAQ;gBAC/B,IAAI,MAAM,IAAI,KAAK,qBAAqB;oBACtC,qBAAO,CAAA,GAAA,6JAAA,CAAA,eAAkB,AAAD,EAAE,OAAO;wBAAE,SAAS,IAAM,QAAQ,CAAC;oBAAM;gBACnE;gBACA,IAAI,MAAM,IAAI,KAAK,qBAAqB;oBACtC,qBAAO,CAAA,GAAA,6JAAA,CAAA,eAAkB,AAAD,EAAE,OAAO;wBAAE;wBAAM,SAAS,IAAM,QAAQ;oBAAO;gBACzE;YACF;YACA,OAAO;QACT;;;;;;AAGN;;AACA,aAAa,WAAW,GAAG;AAE3B,MAAM,oCAAsB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGzC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wQACA;QAED,GAAG,KAAK;kBAER;;;;;;;AAGL,oBAAoB,WAAW,GAAG;AAElC,MAAM,oCAAsB,IAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,YAOzC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,OAAO,EAAE,GAAG,OAAO,EAAE;;IACpE,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAY,AAAD,EAAkB;IAEhD,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;yCAAE;YACd,MAAM;oEAAqB,CAAC;oBAC1B,IAAI,WAAW,OAAO,IAAI,CAAC,WAAW,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBAC5E;oBACF;gBACF;;YAEA,IAAI,MAAM;gBACR,SAAS,gBAAgB,CAAC,aAAa;gBACvC;qDAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;;YACzD;QACF;wCAAG;QAAC;QAAM;KAAQ;IAElB,IAAI,CAAC,MAAM,OAAO;IAElB,MAAM,mBAAmB;QACvB,OAAO;QACP,QAAQ;QACR,KAAK;IACP;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iHACA,oVACA,gBAAgB,CAAC,MAAM,EACvB,iBACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;;AACA,oBAAoB,WAAW,GAAG;AAElC,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAKtC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mOACA,+DACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;MAdP;AAiBN,iBAAiB,WAAW,GAAG;AAE/B,MAAM,yCAA2B,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAK9C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC7C,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wOAAwO;QACrP,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACb,yBACC,6LAAC;oBACC,WAAU;oBACV,MAAK;oBACL,QAAO;oBACP,SAAQ;oBACR,OAAM;8BAEN,cAAA,6LAAC;wBACC,eAAc;wBACd,gBAAe;wBACf,aAAa;wBACb,GAAE;;;;;;;;;;;;;;;;YAKT;;;;;;;;AAGL,yBAAyB,WAAW,GAAG;AAEvC,MAAM,sCAAwB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAK3C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wOAAwO;QACrP,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;oBAAuB,SAAQ;8BAC5C,cAAA,6LAAC;wBAAO,IAAI;wBAAG,IAAI;wBAAG,GAAG;;;;;;;;;;;;;;;;YAG5B;;;;;;;;AAGL,sBAAsB,WAAW,GAAG;AAEpC,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG;AAEhC,MAAM,sCAAwB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,sBAAsB,WAAW,GAAG;AAEpC,MAAM,uBAAuB,CAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;OAVM;AAWN,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 696, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 728, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 762, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/collapsible.tsx"], "sourcesContent": ["'use client'\n\nimport * as React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface CollapsibleContextValue {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n}\n\nconst CollapsibleContext = React.createContext<CollapsibleContextValue | null>(null)\n\nconst useCollapsible = () => {\n  const context = React.useContext(CollapsibleContext)\n  if (!context) {\n    throw new Error('useCollapsible must be used within a Collapsible')\n  }\n  return context\n}\n\ninterface CollapsibleProps {\n  open?: boolean\n  onOpenChange?: (open: boolean) => void\n  children: React.ReactNode\n  className?: string\n}\n\nconst Collapsible = React.forwardRef<HTMLDivElement, CollapsibleProps>(\n  ({ open: controlledOpen, onOpenChange, children, className, ...props }, ref) => {\n    const [internalOpen, setInternalOpen] = React.useState(false)\n    \n    const isControlled = controlledOpen !== undefined\n    const open = isControlled ? controlledOpen : internalOpen\n    \n    const handleOpenChange = React.useCallback((newOpen: boolean) => {\n      if (isControlled) {\n        onOpenChange?.(newOpen)\n      } else {\n        setInternalOpen(newOpen)\n      }\n    }, [isControlled, onOpenChange])\n\n    const contextValue = React.useMemo(() => ({\n      open,\n      onOpenChange: handleOpenChange\n    }), [open, handleOpenChange])\n\n    return (\n      <CollapsibleContext.Provider value={contextValue}>\n        <div ref={ref} className={cn(className)} {...props}>\n          {children}\n        </div>\n      </CollapsibleContext.Provider>\n    )\n  }\n)\nCollapsible.displayName = 'Collapsible'\n\nconst CollapsibleTrigger = React.forwardRef<\n  HTMLButtonElement,\n  React.ButtonHTMLAttributes<HTMLButtonElement> & {\n    asChild?: boolean\n  }\n>(({ asChild = false, children, onClick, ...props }, ref) => {\n  const { onOpenChange, open } = useCollapsible()\n\n  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {\n    onOpenChange(!open)\n    onClick?.(event)\n  }\n\n  if (asChild && React.isValidElement(children)) {\n    return React.cloneElement(children, {\n      ...props,\n      ref,\n      onClick: handleClick,\n    })\n  }\n\n  return (\n    <button ref={ref} onClick={handleClick} {...props}>\n      {children}\n    </button>\n  )\n})\nCollapsibleTrigger.displayName = 'CollapsibleTrigger'\n\nconst CollapsibleContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, children, ...props }, ref) => {\n  const { open } = useCollapsible()\n  const [height, setHeight] = React.useState<number | undefined>(open ? undefined : 0)\n  const contentRef = React.useRef<HTMLDivElement>(null)\n\n  React.useEffect(() => {\n    if (!contentRef.current) return\n\n    if (open) {\n      const scrollHeight = contentRef.current.scrollHeight\n      setHeight(scrollHeight)\n      \n      // Reset to auto after animation\n      const timer = setTimeout(() => {\n        setHeight(undefined)\n      }, 150)\n      \n      return () => clearTimeout(timer)\n    } else {\n      // First set to current height, then to 0 for smooth animation\n      setHeight(contentRef.current.scrollHeight)\n      requestAnimationFrame(() => {\n        setHeight(0)\n      })\n    }\n  }, [open])\n\n  if (!open && height === 0) {\n    return null\n  }\n\n  return (\n    <div\n      ref={ref}\n      className={cn(\n        'overflow-hidden transition-all duration-150 ease-in-out',\n        className\n      )}\n      style={{ height }}\n      {...props}\n    >\n      <div ref={contentRef}>\n        {children}\n      </div>\n    </div>\n  )\n})\nCollapsibleContent.displayName = 'CollapsibleContent'\n\nexport { Collapsible, CollapsibleTrigger, CollapsibleContent }\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;;;AAHA;;;AAUA,MAAM,mCAAqB,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAkC;AAE/E,MAAM,iBAAiB;;IACrB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;IACjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANM;AAeN,MAAM,4BAAc,IAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,WACjC,CAAC,EAAE,MAAM,cAAc,EAAE,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;;IACtE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IAEvD,MAAM,eAAe,mBAAmB;IACxC,MAAM,OAAO,eAAe,iBAAiB;IAE7C,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;qDAAE,CAAC;YAC1C,IAAI,cAAc;gBAChB,eAAe;YACjB,OAAO;gBACL,gBAAgB;YAClB;QACF;oDAAG;QAAC;QAAc;KAAa;IAE/B,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;6CAAE,IAAM,CAAC;gBACxC;gBACA,cAAc;YAChB,CAAC;4CAAG;QAAC;QAAM;KAAiB;IAE5B,qBACE,6LAAC,mBAAmB,QAAQ;QAAC,OAAO;kBAClC,cAAA,6LAAC;YAAI,KAAK;YAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE;YAAa,GAAG,KAAK;sBAC/C;;;;;;;;;;;AAIT;;AAEF,YAAY,WAAW,GAAG;AAE1B,MAAM,mCAAqB,IAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,YAKxC,CAAC,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE;;IACnD,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,GAAG;IAE/B,MAAM,cAAc,CAAC;QACnB,aAAa,CAAC;QACd,UAAU;IACZ;IAEA,IAAI,yBAAW,CAAA,GAAA,6JAAA,CAAA,iBAAoB,AAAD,EAAE,WAAW;QAC7C,qBAAO,CAAA,GAAA,6JAAA,CAAA,eAAkB,AAAD,EAAE,UAAU;YAClC,GAAG,KAAK;YACR;YACA,SAAS;QACX;IACF;IAEA,qBACE,6LAAC;QAAO,KAAK;QAAK,SAAS;QAAc,GAAG,KAAK;kBAC9C;;;;;;AAGP;;QApBiC;;;;QAAA;;;;AAqBjC,mBAAmB,WAAW,GAAG;AAEjC,MAAM,mCAAqB,IAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,YAGxC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;;IACpC,MAAM,EAAE,IAAI,EAAE,GAAG;IACjB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAsB,OAAO,YAAY;IAClF,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAY,AAAD,EAAkB;IAEhD,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;wCAAE;YACd,IAAI,CAAC,WAAW,OAAO,EAAE;YAEzB,IAAI,MAAM;gBACR,MAAM,eAAe,WAAW,OAAO,CAAC,YAAY;gBACpD,UAAU;gBAEV,gCAAgC;gBAChC,MAAM,QAAQ;0DAAW;wBACvB,UAAU;oBACZ;yDAAG;gBAEH;oDAAO,IAAM,aAAa;;YAC5B,OAAO;gBACL,8DAA8D;gBAC9D,UAAU,WAAW,OAAO,CAAC,YAAY;gBACzC;oDAAsB;wBACpB,UAAU;oBACZ;;YACF;QACF;uCAAG;QAAC;KAAK;IAET,IAAI,CAAC,QAAQ,WAAW,GAAG;QACzB,OAAO;IACT;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAEF,OAAO;YAAE;QAAO;QACf,GAAG,KAAK;kBAET,cAAA,6LAAC;YAAI,KAAK;sBACP;;;;;;;;;;;AAIT;;QA7CmB;;;;QAAA;;;;AA8CnB,mBAAmB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 950, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cavi/CaviFilters.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport {\n  Collapsible,\n  CollapsibleContent,\n  CollapsibleTrigger,\n} from '@/components/ui/collapsible'\nimport { \n  Filter, \n  ChevronDown, \n  ChevronUp, \n  X,\n  Search,\n  Cable,\n  Activity,\n  Zap,\n  CheckCircle\n} from 'lucide-react'\nimport { Cavo } from '@/types'\n\ninterface FilterState {\n  search: string\n  tipologia: string[]\n  sistema: string[]\n  utility: string[]\n  formazione: string[]\n  statoInstallazione: string[]\n  collegamento: string[]\n  certificazione: string[]\n  bobina: string[]\n}\n\ninterface CaviFiltersProps {\n  cavi: Cavo[]\n  onFilterChange: (filteredCavi: Cavo[]) => void\n  onSelectionToggle?: () => void\n  selectionEnabled?: boolean\n}\n\nexport default function CaviFilters({\n  cavi,\n  onFilterChange,\n  onSelectionToggle,\n  selectionEnabled = false\n}: CaviFiltersProps) {\n  const [isOpen, setIsOpen] = useState(false)\n  const [filters, setFilters] = useState<FilterState>({\n    search: '',\n    tipologia: [],\n    sistema: [],\n    utility: [],\n    formazione: [],\n    statoInstallazione: [],\n    collegamento: [],\n    certificazione: [],\n    bobina: []\n  })\n\n  // Estrai valori unici per i filtri\n  const getUniqueValues = (field: keyof Cavo) => {\n    return [...new Set(cavi.map(c => c[field]).filter(Boolean))].sort()\n  }\n\n  const tipologie = getUniqueValues('tipologia')\n  const sistemi = getUniqueValues('sistema')\n  const utilities = getUniqueValues('utility')\n  const formazioni = getUniqueValues('formazione')\n  const bobine = getUniqueValues('id_bobina')\n\n  // Applica filtri\n  const applyFilters = (newFilters: FilterState) => {\n    let filtered = cavi\n\n    // Filtro ricerca\n    if (newFilters.search) {\n      const search = newFilters.search.toLowerCase()\n      filtered = filtered.filter(cavo =>\n        cavo.id_cavo?.toString().toLowerCase().includes(search) ||\n        cavo.sistema?.toLowerCase().includes(search) ||\n        cavo.utility?.toLowerCase().includes(search) ||\n        cavo.tipologia?.toLowerCase().includes(search) ||\n        cavo.formazione?.toLowerCase().includes(search) ||\n        cavo.da?.toLowerCase().includes(search) ||\n        cavo.a?.toLowerCase().includes(search)\n      )\n    }\n\n    // Filtri multipli\n    if (newFilters.tipologia.length > 0) {\n      filtered = filtered.filter(c => newFilters.tipologia.includes(c.tipologia || ''))\n    }\n\n    if (newFilters.sistema.length > 0) {\n      filtered = filtered.filter(c => newFilters.sistema.includes(c.sistema || ''))\n    }\n\n    if (newFilters.utility.length > 0) {\n      filtered = filtered.filter(c => newFilters.utility.includes(c.utility || ''))\n    }\n\n    if (newFilters.formazione.length > 0) {\n      filtered = filtered.filter(c => newFilters.formazione.includes(c.formazione || ''))\n    }\n\n    if (newFilters.bobina.length > 0) {\n      filtered = filtered.filter(c => newFilters.bobina.includes(c.id_bobina || ''))\n    }\n\n    // Filtro stato installazione\n    if (newFilters.statoInstallazione.length > 0) {\n      filtered = filtered.filter(cavo => {\n        const isInstalled = cavo.metri_posati > 0\n        return newFilters.statoInstallazione.some(stato => {\n          if (stato === 'installato' && isInstalled) return true\n          if (stato === 'non_installato' && !isInstalled) return true\n          return false\n        })\n      })\n    }\n\n    // Filtro collegamento\n    if (newFilters.collegamento.length > 0) {\n      filtered = filtered.filter(cavo => {\n        return newFilters.collegamento.some(stato => {\n          if (stato === 'collegato' && cavo.collegamento === 3) return true\n          if (stato === 'partenza' && cavo.collegamento === 1) return true\n          if (stato === 'arrivo' && cavo.collegamento === 2) return true\n          if (stato === 'non_collegato' && (!cavo.collegamento || cavo.collegamento === 0)) return true\n          return false\n        })\n      })\n    }\n\n    // Filtro certificazione\n    if (newFilters.certificazione.length > 0) {\n      filtered = filtered.filter(cavo => {\n        return newFilters.certificazione.some(stato => {\n          if (stato === 'certificato' && cavo.certificato) return true\n          if (stato === 'non_certificato' && !cavo.certificato) return true\n          return false\n        })\n      })\n    }\n\n    onFilterChange(filtered)\n  }\n\n  const updateFilter = (key: keyof FilterState, value: any) => {\n    const newFilters = { ...filters, [key]: value }\n    setFilters(newFilters)\n    applyFilters(newFilters)\n  }\n\n  const toggleArrayFilter = (key: keyof FilterState, value: string) => {\n    const currentArray = filters[key] as string[]\n    const newArray = currentArray.includes(value)\n      ? currentArray.filter(item => item !== value)\n      : [...currentArray, value]\n    updateFilter(key, newArray)\n  }\n\n  const clearFilters = () => {\n    const emptyFilters: FilterState = {\n      search: '',\n      tipologia: [],\n      sistema: [],\n      utility: [],\n      formazione: [],\n      statoInstallazione: [],\n      collegamento: [],\n      certificazione: [],\n      bobina: []\n    }\n    setFilters(emptyFilters)\n    applyFilters(emptyFilters)\n  }\n\n  const getActiveFiltersCount = () => {\n    let count = 0\n    if (filters.search) count++\n    count += filters.tipologia.length\n    count += filters.sistema.length\n    count += filters.utility.length\n    count += filters.formazione.length\n    count += filters.statoInstallazione.length\n    count += filters.collegamento.length\n    count += filters.certificazione.length\n    count += filters.bobina.length\n    return count\n  }\n\n  const activeFiltersCount = getActiveFiltersCount()\n\n  return (\n    <Card className=\"mb-4\">\n      <CardHeader className=\"pb-3\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-2\">\n            <Filter className=\"h-4 w-4\" />\n            <CardTitle className=\"text-base\">Filtri</CardTitle>\n            {activeFiltersCount > 0 && (\n              <Badge variant=\"secondary\">{activeFiltersCount}</Badge>\n            )}\n          </div>\n          \n          <div className=\"flex items-center space-x-2\">\n            {selectionEnabled !== undefined && (\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={onSelectionToggle}\n              >\n                {selectionEnabled ? 'Disabilita Selezione' : 'Abilita Selezione'}\n              </Button>\n            )}\n            \n            {activeFiltersCount > 0 && (\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={clearFilters}\n              >\n                <X className=\"h-3 w-3 mr-1\" />\n                Pulisci\n              </Button>\n            )}\n            \n            <Collapsible open={isOpen} onOpenChange={setIsOpen}>\n              <CollapsibleTrigger asChild>\n                <Button variant=\"ghost\" size=\"sm\">\n                  {isOpen ? <ChevronUp className=\"h-4 w-4\" /> : <ChevronDown className=\"h-4 w-4\" />}\n                </Button>\n              </CollapsibleTrigger>\n            </Collapsible>\n          </div>\n        </div>\n\n        {/* Ricerca sempre visibile */}\n        <div className=\"flex items-center space-x-2\">\n          <Search className=\"h-4 w-4 text-muted-foreground\" />\n          <Input\n            placeholder=\"Cerca cavi...\"\n            value={filters.search}\n            onChange={(e) => updateFilter('search', e.target.value)}\n            className=\"flex-1\"\n          />\n        </div>\n      </CardHeader>\n\n      <Collapsible open={isOpen} onOpenChange={setIsOpen}>\n        <CollapsibleContent>\n          <CardContent className=\"pt-0\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n              \n              {/* Tipologia */}\n              <div className=\"space-y-2\">\n                <Label className=\"text-sm font-medium\">Tipologia</Label>\n                <div className=\"space-y-1 max-h-32 overflow-y-auto\">\n                  {tipologie.map(tip => (\n                    <div key={tip} className=\"flex items-center space-x-2\">\n                      <Checkbox\n                        id={`tip-${tip}`}\n                        checked={filters.tipologia.includes(tip)}\n                        onCheckedChange={() => toggleArrayFilter('tipologia', tip)}\n                      />\n                      <Label htmlFor={`tip-${tip}`} className=\"text-xs\">{tip}</Label>\n                    </div>\n                  ))}\n                </div>\n              </div>\n\n              {/* Sistema */}\n              <div className=\"space-y-2\">\n                <Label className=\"text-sm font-medium\">Sistema</Label>\n                <div className=\"space-y-1 max-h-32 overflow-y-auto\">\n                  {sistemi.map(sis => (\n                    <div key={sis} className=\"flex items-center space-x-2\">\n                      <Checkbox\n                        id={`sis-${sis}`}\n                        checked={filters.sistema.includes(sis)}\n                        onCheckedChange={() => toggleArrayFilter('sistema', sis)}\n                      />\n                      <Label htmlFor={`sis-${sis}`} className=\"text-xs\">{sis}</Label>\n                    </div>\n                  ))}\n                </div>\n              </div>\n\n              {/* Utility */}\n              <div className=\"space-y-2\">\n                <Label className=\"text-sm font-medium\">Utility</Label>\n                <div className=\"space-y-1 max-h-32 overflow-y-auto\">\n                  {utilities.map(util => (\n                    <div key={util} className=\"flex items-center space-x-2\">\n                      <Checkbox\n                        id={`util-${util}`}\n                        checked={filters.utility.includes(util)}\n                        onCheckedChange={() => toggleArrayFilter('utility', util)}\n                      />\n                      <Label htmlFor={`util-${util}`} className=\"text-xs\">{util}</Label>\n                    </div>\n                  ))}\n                </div>\n              </div>\n\n              {/* Stato Installazione */}\n              <div className=\"space-y-2\">\n                <Label className=\"text-sm font-medium flex items-center\">\n                  <Activity className=\"h-3 w-3 mr-1\" />\n                  Installazione\n                </Label>\n                <div className=\"space-y-1\">\n                  {[\n                    { value: 'installato', label: 'Installato' },\n                    { value: 'non_installato', label: 'Da Installare' }\n                  ].map(stato => (\n                    <div key={stato.value} className=\"flex items-center space-x-2\">\n                      <Checkbox\n                        id={`inst-${stato.value}`}\n                        checked={filters.statoInstallazione.includes(stato.value)}\n                        onCheckedChange={() => toggleArrayFilter('statoInstallazione', stato.value)}\n                      />\n                      <Label htmlFor={`inst-${stato.value}`} className=\"text-xs\">{stato.label}</Label>\n                    </div>\n                  ))}\n                </div>\n              </div>\n\n              {/* Collegamento */}\n              <div className=\"space-y-2\">\n                <Label className=\"text-sm font-medium flex items-center\">\n                  <Zap className=\"h-3 w-3 mr-1\" />\n                  Collegamento\n                </Label>\n                <div className=\"space-y-1\">\n                  {[\n                    { value: 'collegato', label: 'Collegato' },\n                    { value: 'partenza', label: 'Lato Partenza' },\n                    { value: 'arrivo', label: 'Lato Arrivo' },\n                    { value: 'non_collegato', label: 'Non Collegato' }\n                  ].map(stato => (\n                    <div key={stato.value} className=\"flex items-center space-x-2\">\n                      <Checkbox\n                        id={`coll-${stato.value}`}\n                        checked={filters.collegamento.includes(stato.value)}\n                        onCheckedChange={() => toggleArrayFilter('collegamento', stato.value)}\n                      />\n                      <Label htmlFor={`coll-${stato.value}`} className=\"text-xs\">{stato.label}</Label>\n                    </div>\n                  ))}\n                </div>\n              </div>\n\n              {/* Certificazione */}\n              <div className=\"space-y-2\">\n                <Label className=\"text-sm font-medium flex items-center\">\n                  <CheckCircle className=\"h-3 w-3 mr-1\" />\n                  Certificazione\n                </Label>\n                <div className=\"space-y-1\">\n                  {[\n                    { value: 'certificato', label: 'Certificato' },\n                    { value: 'non_certificato', label: 'Non Certificato' }\n                  ].map(stato => (\n                    <div key={stato.value} className=\"flex items-center space-x-2\">\n                      <Checkbox\n                        id={`cert-${stato.value}`}\n                        checked={filters.certificazione.includes(stato.value)}\n                        onCheckedChange={() => toggleArrayFilter('certificazione', stato.value)}\n                      />\n                      <Label htmlFor={`cert-${stato.value}`} className=\"text-xs\">{stato.label}</Label>\n                    </div>\n                  ))}\n                </div>\n              </div>\n\n            </div>\n          </CardContent>\n        </CollapsibleContent>\n      </Collapsible>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAQA;AACA;AACA;AACA;AAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AArBA;;;;;;;;;;AAqDe,SAAS,YAAY,EAClC,IAAI,EACJ,cAAc,EACd,iBAAiB,EACjB,mBAAmB,KAAK,EACP;;IACjB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;QAClD,QAAQ;QACR,WAAW,EAAE;QACb,SAAS,EAAE;QACX,SAAS,EAAE;QACX,YAAY,EAAE;QACd,oBAAoB,EAAE;QACtB,cAAc,EAAE;QAChB,gBAAgB,EAAE;QAClB,QAAQ,EAAE;IACZ;IAEA,mCAAmC;IACnC,MAAM,kBAAkB,CAAC;QACvB,OAAO;eAAI,IAAI,IAAI,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC;SAAU,CAAC,IAAI;IACnE;IAEA,MAAM,YAAY,gBAAgB;IAClC,MAAM,UAAU,gBAAgB;IAChC,MAAM,YAAY,gBAAgB;IAClC,MAAM,aAAa,gBAAgB;IACnC,MAAM,SAAS,gBAAgB;IAE/B,iBAAiB;IACjB,MAAM,eAAe,CAAC;QACpB,IAAI,WAAW;QAEf,iBAAiB;QACjB,IAAI,WAAW,MAAM,EAAE;YACrB,MAAM,SAAS,WAAW,MAAM,CAAC,WAAW;YAC5C,WAAW,SAAS,MAAM,CAAC,CAAA,OACzB,KAAK,OAAO,EAAE,WAAW,cAAc,SAAS,WAChD,KAAK,OAAO,EAAE,cAAc,SAAS,WACrC,KAAK,OAAO,EAAE,cAAc,SAAS,WACrC,KAAK,SAAS,EAAE,cAAc,SAAS,WACvC,KAAK,UAAU,EAAE,cAAc,SAAS,WACxC,KAAK,EAAE,EAAE,cAAc,SAAS,WAChC,KAAK,CAAC,EAAE,cAAc,SAAS;QAEnC;QAEA,kBAAkB;QAClB,IAAI,WAAW,SAAS,CAAC,MAAM,GAAG,GAAG;YACnC,WAAW,SAAS,MAAM,CAAC,CAAA,IAAK,WAAW,SAAS,CAAC,QAAQ,CAAC,EAAE,SAAS,IAAI;QAC/E;QAEA,IAAI,WAAW,OAAO,CAAC,MAAM,GAAG,GAAG;YACjC,WAAW,SAAS,MAAM,CAAC,CAAA,IAAK,WAAW,OAAO,CAAC,QAAQ,CAAC,EAAE,OAAO,IAAI;QAC3E;QAEA,IAAI,WAAW,OAAO,CAAC,MAAM,GAAG,GAAG;YACjC,WAAW,SAAS,MAAM,CAAC,CAAA,IAAK,WAAW,OAAO,CAAC,QAAQ,CAAC,EAAE,OAAO,IAAI;QAC3E;QAEA,IAAI,WAAW,UAAU,CAAC,MAAM,GAAG,GAAG;YACpC,WAAW,SAAS,MAAM,CAAC,CAAA,IAAK,WAAW,UAAU,CAAC,QAAQ,CAAC,EAAE,UAAU,IAAI;QACjF;QAEA,IAAI,WAAW,MAAM,CAAC,MAAM,GAAG,GAAG;YAChC,WAAW,SAAS,MAAM,CAAC,CAAA,IAAK,WAAW,MAAM,CAAC,QAAQ,CAAC,EAAE,SAAS,IAAI;QAC5E;QAEA,6BAA6B;QAC7B,IAAI,WAAW,kBAAkB,CAAC,MAAM,GAAG,GAAG;YAC5C,WAAW,SAAS,MAAM,CAAC,CAAA;gBACzB,MAAM,cAAc,KAAK,YAAY,GAAG;gBACxC,OAAO,WAAW,kBAAkB,CAAC,IAAI,CAAC,CAAA;oBACxC,IAAI,UAAU,gBAAgB,aAAa,OAAO;oBAClD,IAAI,UAAU,oBAAoB,CAAC,aAAa,OAAO;oBACvD,OAAO;gBACT;YACF;QACF;QAEA,sBAAsB;QACtB,IAAI,WAAW,YAAY,CAAC,MAAM,GAAG,GAAG;YACtC,WAAW,SAAS,MAAM,CAAC,CAAA;gBACzB,OAAO,WAAW,YAAY,CAAC,IAAI,CAAC,CAAA;oBAClC,IAAI,UAAU,eAAe,KAAK,YAAY,KAAK,GAAG,OAAO;oBAC7D,IAAI,UAAU,cAAc,KAAK,YAAY,KAAK,GAAG,OAAO;oBAC5D,IAAI,UAAU,YAAY,KAAK,YAAY,KAAK,GAAG,OAAO;oBAC1D,IAAI,UAAU,mBAAmB,CAAC,CAAC,KAAK,YAAY,IAAI,KAAK,YAAY,KAAK,CAAC,GAAG,OAAO;oBACzF,OAAO;gBACT;YACF;QACF;QAEA,wBAAwB;QACxB,IAAI,WAAW,cAAc,CAAC,MAAM,GAAG,GAAG;YACxC,WAAW,SAAS,MAAM,CAAC,CAAA;gBACzB,OAAO,WAAW,cAAc,CAAC,IAAI,CAAC,CAAA;oBACpC,IAAI,UAAU,iBAAiB,KAAK,WAAW,EAAE,OAAO;oBACxD,IAAI,UAAU,qBAAqB,CAAC,KAAK,WAAW,EAAE,OAAO;oBAC7D,OAAO;gBACT;YACF;QACF;QAEA,eAAe;IACjB;IAEA,MAAM,eAAe,CAAC,KAAwB;QAC5C,MAAM,aAAa;YAAE,GAAG,OAAO;YAAE,CAAC,IAAI,EAAE;QAAM;QAC9C,WAAW;QACX,aAAa;IACf;IAEA,MAAM,oBAAoB,CAAC,KAAwB;QACjD,MAAM,eAAe,OAAO,CAAC,IAAI;QACjC,MAAM,WAAW,aAAa,QAAQ,CAAC,SACnC,aAAa,MAAM,CAAC,CAAA,OAAQ,SAAS,SACrC;eAAI;YAAc;SAAM;QAC5B,aAAa,KAAK;IACpB;IAEA,MAAM,eAAe;QACnB,MAAM,eAA4B;YAChC,QAAQ;YACR,WAAW,EAAE;YACb,SAAS,EAAE;YACX,SAAS,EAAE;YACX,YAAY,EAAE;YACd,oBAAoB,EAAE;YACtB,cAAc,EAAE;YAChB,gBAAgB,EAAE;YAClB,QAAQ,EAAE;QACZ;QACA,WAAW;QACX,aAAa;IACf;IAEA,MAAM,wBAAwB;QAC5B,IAAI,QAAQ;QACZ,IAAI,QAAQ,MAAM,EAAE;QACpB,SAAS,QAAQ,SAAS,CAAC,MAAM;QACjC,SAAS,QAAQ,OAAO,CAAC,MAAM;QAC/B,SAAS,QAAQ,OAAO,CAAC,MAAM;QAC/B,SAAS,QAAQ,UAAU,CAAC,MAAM;QAClC,SAAS,QAAQ,kBAAkB,CAAC,MAAM;QAC1C,SAAS,QAAQ,YAAY,CAAC,MAAM;QACpC,SAAS,QAAQ,cAAc,CAAC,MAAM;QACtC,SAAS,QAAQ,MAAM,CAAC,MAAM;QAC9B,OAAO;IACT;IAEA,MAAM,qBAAqB;IAE3B,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAY;;;;;;oCAChC,qBAAqB,mBACpB,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAa;;;;;;;;;;;;0CAIhC,6LAAC;gCAAI,WAAU;;oCACZ,qBAAqB,2BACpB,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;kDAER,mBAAmB,yBAAyB;;;;;;oCAIhD,qBAAqB,mBACpB,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;;0DAET,6LAAC,+LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAKlC,6LAAC,0IAAA,CAAA,cAAW;wCAAC,MAAM;wCAAQ,cAAc;kDACvC,cAAA,6LAAC,0IAAA,CAAA,qBAAkB;4CAAC,OAAO;sDACzB,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;0DAC1B,uBAAS,6LAAC,mNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;yEAAe,6LAAC,uNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ/E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC,oIAAA,CAAA,QAAK;gCACJ,aAAY;gCACZ,OAAO,QAAQ,MAAM;gCACrB,UAAU,CAAC,IAAM,aAAa,UAAU,EAAE,MAAM,CAAC,KAAK;gCACtD,WAAU;;;;;;;;;;;;;;;;;;0BAKhB,6LAAC,0IAAA,CAAA,cAAW;gBAAC,MAAM;gBAAQ,cAAc;0BACvC,cAAA,6LAAC,0IAAA,CAAA,qBAAkB;8BACjB,cAAA,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,6LAAC;4BAAI,WAAU;;8CAGb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,WAAU;sDAAsB;;;;;;sDACvC,6LAAC;4CAAI,WAAU;sDACZ,UAAU,GAAG,CAAC,CAAA,oBACb,6LAAC;oDAAc,WAAU;;sEACvB,6LAAC,uIAAA,CAAA,WAAQ;4DACP,IAAI,CAAC,IAAI,EAAE,KAAK;4DAChB,SAAS,QAAQ,SAAS,CAAC,QAAQ,CAAC;4DACpC,iBAAiB,IAAM,kBAAkB,aAAa;;;;;;sEAExD,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAS,CAAC,IAAI,EAAE,KAAK;4DAAE,WAAU;sEAAW;;;;;;;mDAN3C;;;;;;;;;;;;;;;;8CAahB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,WAAU;sDAAsB;;;;;;sDACvC,6LAAC;4CAAI,WAAU;sDACZ,QAAQ,GAAG,CAAC,CAAA,oBACX,6LAAC;oDAAc,WAAU;;sEACvB,6LAAC,uIAAA,CAAA,WAAQ;4DACP,IAAI,CAAC,IAAI,EAAE,KAAK;4DAChB,SAAS,QAAQ,OAAO,CAAC,QAAQ,CAAC;4DAClC,iBAAiB,IAAM,kBAAkB,WAAW;;;;;;sEAEtD,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAS,CAAC,IAAI,EAAE,KAAK;4DAAE,WAAU;sEAAW;;;;;;;mDAN3C;;;;;;;;;;;;;;;;8CAahB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,WAAU;sDAAsB;;;;;;sDACvC,6LAAC;4CAAI,WAAU;sDACZ,UAAU,GAAG,CAAC,CAAA,qBACb,6LAAC;oDAAe,WAAU;;sEACxB,6LAAC,uIAAA,CAAA,WAAQ;4DACP,IAAI,CAAC,KAAK,EAAE,MAAM;4DAClB,SAAS,QAAQ,OAAO,CAAC,QAAQ,CAAC;4DAClC,iBAAiB,IAAM,kBAAkB,WAAW;;;;;;sEAEtD,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAS,CAAC,KAAK,EAAE,MAAM;4DAAE,WAAU;sEAAW;;;;;;;mDAN7C;;;;;;;;;;;;;;;;8CAahB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,WAAU;;8DACf,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGvC,6LAAC;4CAAI,WAAU;sDACZ;gDACC;oDAAE,OAAO;oDAAc,OAAO;gDAAa;gDAC3C;oDAAE,OAAO;oDAAkB,OAAO;gDAAgB;6CACnD,CAAC,GAAG,CAAC,CAAA,sBACJ,6LAAC;oDAAsB,WAAU;;sEAC/B,6LAAC,uIAAA,CAAA,WAAQ;4DACP,IAAI,CAAC,KAAK,EAAE,MAAM,KAAK,EAAE;4DACzB,SAAS,QAAQ,kBAAkB,CAAC,QAAQ,CAAC,MAAM,KAAK;4DACxD,iBAAiB,IAAM,kBAAkB,sBAAsB,MAAM,KAAK;;;;;;sEAE5E,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAS,CAAC,KAAK,EAAE,MAAM,KAAK,EAAE;4DAAE,WAAU;sEAAW,MAAM,KAAK;;;;;;;mDAN/D,MAAM,KAAK;;;;;;;;;;;;;;;;8CAa3B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,WAAU;;8DACf,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGlC,6LAAC;4CAAI,WAAU;sDACZ;gDACC;oDAAE,OAAO;oDAAa,OAAO;gDAAY;gDACzC;oDAAE,OAAO;oDAAY,OAAO;gDAAgB;gDAC5C;oDAAE,OAAO;oDAAU,OAAO;gDAAc;gDACxC;oDAAE,OAAO;oDAAiB,OAAO;gDAAgB;6CAClD,CAAC,GAAG,CAAC,CAAA,sBACJ,6LAAC;oDAAsB,WAAU;;sEAC/B,6LAAC,uIAAA,CAAA,WAAQ;4DACP,IAAI,CAAC,KAAK,EAAE,MAAM,KAAK,EAAE;4DACzB,SAAS,QAAQ,YAAY,CAAC,QAAQ,CAAC,MAAM,KAAK;4DAClD,iBAAiB,IAAM,kBAAkB,gBAAgB,MAAM,KAAK;;;;;;sEAEtE,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAS,CAAC,KAAK,EAAE,MAAM,KAAK,EAAE;4DAAE,WAAU;sEAAW,MAAM,KAAK;;;;;;;mDAN/D,MAAM,KAAK;;;;;;;;;;;;;;;;8CAa3B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,WAAU;;8DACf,6LAAC,8NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAG1C,6LAAC;4CAAI,WAAU;sDACZ;gDACC;oDAAE,OAAO;oDAAe,OAAO;gDAAc;gDAC7C;oDAAE,OAAO;oDAAmB,OAAO;gDAAkB;6CACtD,CAAC,GAAG,CAAC,CAAA,sBACJ,6LAAC;oDAAsB,WAAU;;sEAC/B,6LAAC,uIAAA,CAAA,WAAQ;4DACP,IAAI,CAAC,KAAK,EAAE,MAAM,KAAK,EAAE;4DACzB,SAAS,QAAQ,cAAc,CAAC,QAAQ,CAAC,MAAM,KAAK;4DACpD,iBAAiB,IAAM,kBAAkB,kBAAkB,MAAM,KAAK;;;;;;sEAExE,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAS,CAAC,KAAK,EAAE,MAAM,KAAK,EAAE;4DAAE,WAAU;sEAAW,MAAM,KAAK;;;;;;;mDAN/D,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBzC;GAvVwB;KAAA", "debugId": null}}, {"offset": {"line": 1682, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cavi/CaviTable.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useMemo, useEffect } from 'react'\nimport {\n  Table,\n  TableBody,\n  TableCell,\n  TableHead,\n  TableHeader,\n  TableRow,\n} from '@/components/ui/table'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Cavo } from '@/types'\nimport CaviFilters from './CaviFilters'\nimport {\n  MoreHorizontal,\n  Cable,\n  Settings,\n  Zap,\n  CheckCircle,\n  AlertCircle,\n  Clock,\n  Package\n} from 'lucide-react'\n\ninterface CaviTableProps {\n  cavi: Cavo[]\n  loading?: boolean\n  selectionEnabled?: boolean\n  selectedCavi?: number[]\n  onSelectionChange?: (selectedIds: number[]) => void\n  onStatusAction?: (cavo: Cavo, action: string) => void\n  onContextMenuAction?: (cavo: Cavo, action: string) => void\n}\n\nexport default function CaviTable({\n  cavi = [],\n  loading = false,\n  selectionEnabled = false,\n  selectedCavi = [],\n  onSelectionChange,\n  onStatusAction,\n  onContextMenuAction\n}: CaviTableProps) {\n  const [filteredCavi, setFilteredCavi] = useState(cavi)\n  const [internalSelectionEnabled, setInternalSelectionEnabled] = useState(selectionEnabled)\n\n  // Aggiorna i cavi filtrati quando cambiano i cavi originali\n  useEffect(() => {\n    setFilteredCavi(cavi)\n  }, [cavi])\n\n  // Gestione filtri\n  const handleFilterChange = (filtered: Cavo[]) => {\n    setFilteredCavi(filtered)\n  }\n\n  const handleSelectionToggle = () => {\n    setInternalSelectionEnabled(!internalSelectionEnabled)\n  }\n\n  // Gestione selezione\n  const handleSelectAll = (checked: boolean) => {\n    if (onSelectionChange) {\n      onSelectionChange(checked ? filteredCavi.map(c => c.id_cavo) : [])\n    }\n  }\n\n  const handleSelectCavo = (cavoId: number, checked: boolean) => {\n    if (onSelectionChange) {\n      const newSelection = checked\n        ? [...selectedCavi, cavoId]\n        : selectedCavi.filter(id => id !== cavoId)\n      onSelectionChange(newSelection)\n    }\n  }\n\n  // Funzioni di utilità per lo stato\n  const getStatusBadge = (cavo: Cavo) => {\n    const isInstalled = cavo.metri_posati > 0\n    const isConnected = cavo.collegamento === 3\n    const isCertified = cavo.certificato\n\n    if (isCertified) {\n      return <Badge className=\"bg-green-100 text-green-800\">Certificato</Badge>\n    } else if (isConnected) {\n      return <Badge className=\"bg-blue-100 text-blue-800\">Collegato</Badge>\n    } else if (isInstalled) {\n      return <Badge className=\"bg-yellow-100 text-yellow-800\">Installato</Badge>\n    } else {\n      return <Badge variant=\"outline\">Da Installare</Badge>\n    }\n  }\n\n  const getStatusButton = (cavo: Cavo) => {\n    const isInstalled = cavo.metri_posati > 0\n\n    if (!isInstalled) {\n      return (\n        <Button\n          size=\"sm\"\n          variant=\"outline\"\n          onClick={() => onStatusAction?.(cavo, 'insert_meters')}\n          className=\"text-xs\"\n        >\n          <Package className=\"h-3 w-3 mr-1\" />\n          Inserisci Metri\n        </Button>\n      )\n    } else {\n      return (\n        <Button\n          size=\"sm\"\n          variant=\"outline\"\n          onClick={() => onStatusAction?.(cavo, 'modify_reel')}\n          className=\"text-xs\"\n        >\n          <Settings className=\"h-3 w-3 mr-1\" />\n          Modifica Bobina\n        </Button>\n      )\n    }\n  }\n\n  const getConnectionIndicator = (collegamento: number) => {\n    switch (collegamento) {\n      case 1:\n        return <div className=\"w-3 h-3 rounded-full bg-red-500\" title=\"Lato partenza\" />\n      case 2:\n        return <div className=\"w-3 h-3 rounded-full bg-yellow-500\" title=\"Lato arrivo\" />\n      case 3:\n        return <div className=\"w-3 h-3 rounded-full bg-green-500\" title=\"Collegato\" />\n      default:\n        return <div className=\"w-3 h-3 rounded-full bg-gray-300\" title=\"Non collegato\" />\n    }\n  }\n\n  if (loading) {\n    return (\n      <Card>\n        <CardContent className=\"p-6\">\n          <div className=\"text-center\">Caricamento cavi...</div>\n        </CardContent>\n      </Card>\n    )\n  }\n\n  return (\n    <div>\n      {/* Filtri intelligenti */}\n      <CaviFilters\n        cavi={cavi}\n        onFilterChange={handleFilterChange}\n        onSelectionToggle={handleSelectionToggle}\n        selectionEnabled={internalSelectionEnabled}\n      />\n\n      {/* Tabella */}\n      <Card>\n        <CardHeader>\n          <div className=\"flex items-center justify-between\">\n            <CardTitle className=\"flex items-center space-x-2\">\n              <Cable className=\"h-5 w-5\" />\n              <span>Elenco Cavi ({filteredCavi.length})</span>\n            </CardTitle>\n\n            {internalSelectionEnabled && selectedCavi.length > 0 && (\n              <Badge variant=\"secondary\">\n                {selectedCavi.length} selezionati\n              </Badge>\n            )}\n          </div>\n        </CardHeader>\n\n      <CardContent>\n        <Table>\n          <TableHeader>\n            <TableRow>\n              {internalSelectionEnabled && (\n                <TableHead className=\"w-12\">\n                  <Checkbox\n                    checked={selectedCavi.length === filteredCavi.length && filteredCavi.length > 0}\n                    onCheckedChange={handleSelectAll}\n                  />\n                </TableHead>\n              )}\n              <TableHead>ID Cavo</TableHead>\n              <TableHead>Sistema</TableHead>\n              <TableHead>Utility</TableHead>\n              <TableHead>Tipologia</TableHead>\n              <TableHead>Formazione</TableHead>\n              <TableHead>Da</TableHead>\n              <TableHead>A</TableHead>\n              <TableHead>Metri Teorici</TableHead>\n              <TableHead>Metri Posati</TableHead>\n              <TableHead>Bobina</TableHead>\n              <TableHead>Collegamento</TableHead>\n              <TableHead>Stato</TableHead>\n              <TableHead>Azioni</TableHead>\n              <TableHead className=\"w-12\"></TableHead>\n            </TableRow>\n          </TableHeader>\n          <TableBody>\n            {filteredCavi.map((cavo) => (\n              <TableRow key={cavo.id_cavo}>\n                {internalSelectionEnabled && (\n                  <TableCell>\n                    <Checkbox\n                      checked={selectedCavi.includes(cavo.id_cavo)}\n                      onCheckedChange={(checked) => handleSelectCavo(cavo.id_cavo, checked as boolean)}\n                    />\n                  </TableCell>\n                )}\n                <TableCell className=\"font-medium\">{cavo.id_cavo}</TableCell>\n                <TableCell>{cavo.sistema}</TableCell>\n                <TableCell>{cavo.utility}</TableCell>\n                <TableCell>{cavo.tipologia}</TableCell>\n                <TableCell>{cavo.formazione}</TableCell>\n                <TableCell>{cavo.da}</TableCell>\n                <TableCell>{cavo.a}</TableCell>\n                <TableCell>{cavo.metri_teorici}</TableCell>\n                <TableCell>{cavo.metri_posati || 0}</TableCell>\n                <TableCell>{cavo.id_bobina || 'N/A'}</TableCell>\n                <TableCell>\n                  <div className=\"flex items-center space-x-2\">\n                    {getConnectionIndicator(cavo.collegamento)}\n                    <span className=\"text-xs\">\n                      {cavo.collegamento === 1 ? 'Partenza' :\n                       cavo.collegamento === 2 ? 'Arrivo' :\n                       cavo.collegamento === 3 ? 'Collegato' : 'Non collegato'}\n                    </span>\n                  </div>\n                </TableCell>\n                <TableCell>{getStatusBadge(cavo)}</TableCell>\n                <TableCell>{getStatusButton(cavo)}</TableCell>\n                <TableCell>\n                  <DropdownMenu>\n                    <DropdownMenuTrigger asChild>\n                      <Button variant=\"ghost\" size=\"sm\">\n                        <MoreHorizontal className=\"h-4 w-4\" />\n                      </Button>\n                    </DropdownMenuTrigger>\n                    <DropdownMenuContent align=\"end\">\n                      <DropdownMenuItem onClick={() => onContextMenuAction?.(cavo, 'edit')}>\n                        Modifica Cavo\n                      </DropdownMenuItem>\n                      <DropdownMenuItem onClick={() => onContextMenuAction?.(cavo, 'connections')}>\n                        Collegamenti\n                      </DropdownMenuItem>\n                      <DropdownMenuItem onClick={() => onContextMenuAction?.(cavo, 'certification')}>\n                        Certificazione\n                      </DropdownMenuItem>\n                      <DropdownMenuItem onClick={() => onContextMenuAction?.(cavo, 'delete')} className=\"text-red-600\">\n                        Elimina Cavo\n                      </DropdownMenuItem>\n                    </DropdownMenuContent>\n                  </DropdownMenu>\n                </TableCell>\n              </TableRow>\n            ))}\n          </TableBody>\n        </Table>\n\n        {filteredCavi.length === 0 && (\n          <div className=\"text-center py-8 text-muted-foreground\">\n            Nessun cavo trovato con i filtri selezionati\n          </div>\n        )}\n      </CardContent>\n    </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AAMA;AAEA;AACA;AAAA;AAAA;AAAA;;;AAvBA;;;;;;;;;;AA4Ce,SAAS,UAAU,EAChC,OAAO,EAAE,EACT,UAAU,KAAK,EACf,mBAAmB,KAAK,EACxB,eAAe,EAAE,EACjB,iBAAiB,EACjB,cAAc,EACd,mBAAmB,EACJ;;IACf,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzE,4DAA4D;IAC5D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,gBAAgB;QAClB;8BAAG;QAAC;KAAK;IAET,kBAAkB;IAClB,MAAM,qBAAqB,CAAC;QAC1B,gBAAgB;IAClB;IAEA,MAAM,wBAAwB;QAC5B,4BAA4B,CAAC;IAC/B;IAEA,qBAAqB;IACrB,MAAM,kBAAkB,CAAC;QACvB,IAAI,mBAAmB;YACrB,kBAAkB,UAAU,aAAa,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO,IAAI,EAAE;QACnE;IACF;IAEA,MAAM,mBAAmB,CAAC,QAAgB;QACxC,IAAI,mBAAmB;YACrB,MAAM,eAAe,UACjB;mBAAI;gBAAc;aAAO,GACzB,aAAa,MAAM,CAAC,CAAA,KAAM,OAAO;YACrC,kBAAkB;QACpB;IACF;IAEA,mCAAmC;IACnC,MAAM,iBAAiB,CAAC;QACtB,MAAM,cAAc,KAAK,YAAY,GAAG;QACxC,MAAM,cAAc,KAAK,YAAY,KAAK;QAC1C,MAAM,cAAc,KAAK,WAAW;QAEpC,IAAI,aAAa;YACf,qBAAO,6LAAC,oIAAA,CAAA,QAAK;gBAAC,WAAU;0BAA8B;;;;;;QACxD,OAAO,IAAI,aAAa;YACtB,qBAAO,6LAAC,oIAAA,CAAA,QAAK;gBAAC,WAAU;0BAA4B;;;;;;QACtD,OAAO,IAAI,aAAa;YACtB,qBAAO,6LAAC,oIAAA,CAAA,QAAK;gBAAC,WAAU;0BAAgC;;;;;;QAC1D,OAAO;YACL,qBAAO,6LAAC,oIAAA,CAAA,QAAK;gBAAC,SAAQ;0BAAU;;;;;;QAClC;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,cAAc,KAAK,YAAY,GAAG;QAExC,IAAI,CAAC,aAAa;YAChB,qBACE,6LAAC,qIAAA,CAAA,SAAM;gBACL,MAAK;gBACL,SAAQ;gBACR,SAAS,IAAM,iBAAiB,MAAM;gBACtC,WAAU;;kCAEV,6LAAC,2MAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;QAI1C,OAAO;YACL,qBACE,6LAAC,qIAAA,CAAA,SAAM;gBACL,MAAK;gBACL,SAAQ;gBACR,SAAS,IAAM,iBAAiB,MAAM;gBACtC,WAAU;;kCAEV,6LAAC,6MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;QAI3C;IACF;IAEA,MAAM,yBAAyB,CAAC;QAC9B,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC;oBAAI,WAAU;oBAAkC,OAAM;;;;;;YAChE,KAAK;gBACH,qBAAO,6LAAC;oBAAI,WAAU;oBAAqC,OAAM;;;;;;YACnE,KAAK;gBACH,qBAAO,6LAAC;oBAAI,WAAU;oBAAoC,OAAM;;;;;;YAClE;gBACE,qBAAO,6LAAC;oBAAI,WAAU;oBAAmC,OAAM;;;;;;QACnE;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC,mIAAA,CAAA,OAAI;sBACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC;oBAAI,WAAU;8BAAc;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,6LAAC;;0BAEC,6LAAC,4IAAA,CAAA,UAAW;gBACV,MAAM;gBACN,gBAAgB;gBAChB,mBAAmB;gBACnB,kBAAkB;;;;;;0BAIpB,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;;gDAAK;gDAAc,aAAa,MAAM;gDAAC;;;;;;;;;;;;;gCAGzC,4BAA4B,aAAa,MAAM,GAAG,mBACjD,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;;wCACZ,aAAa,MAAM;wCAAC;;;;;;;;;;;;;;;;;;kCAM/B,6LAAC,mIAAA,CAAA,cAAW;;0CACV,6LAAC,oIAAA,CAAA,QAAK;;kDACJ,6LAAC,oIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;;gDACN,0CACC,6LAAC,oIAAA,CAAA,YAAS;oDAAC,WAAU;8DACnB,cAAA,6LAAC,uIAAA,CAAA,WAAQ;wDACP,SAAS,aAAa,MAAM,KAAK,aAAa,MAAM,IAAI,aAAa,MAAM,GAAG;wDAC9E,iBAAiB;;;;;;;;;;;8DAIvB,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAGzB,6LAAC,oIAAA,CAAA,YAAS;kDACP,aAAa,GAAG,CAAC,CAAC,qBACjB,6LAAC,oIAAA,CAAA,WAAQ;;oDACN,0CACC,6LAAC,oIAAA,CAAA,YAAS;kEACR,cAAA,6LAAC,uIAAA,CAAA,WAAQ;4DACP,SAAS,aAAa,QAAQ,CAAC,KAAK,OAAO;4DAC3C,iBAAiB,CAAC,UAAY,iBAAiB,KAAK,OAAO,EAAE;;;;;;;;;;;kEAInE,6LAAC,oIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAe,KAAK,OAAO;;;;;;kEAChD,6LAAC,oIAAA,CAAA,YAAS;kEAAE,KAAK,OAAO;;;;;;kEACxB,6LAAC,oIAAA,CAAA,YAAS;kEAAE,KAAK,OAAO;;;;;;kEACxB,6LAAC,oIAAA,CAAA,YAAS;kEAAE,KAAK,SAAS;;;;;;kEAC1B,6LAAC,oIAAA,CAAA,YAAS;kEAAE,KAAK,UAAU;;;;;;kEAC3B,6LAAC,oIAAA,CAAA,YAAS;kEAAE,KAAK,EAAE;;;;;;kEACnB,6LAAC,oIAAA,CAAA,YAAS;kEAAE,KAAK,CAAC;;;;;;kEAClB,6LAAC,oIAAA,CAAA,YAAS;kEAAE,KAAK,aAAa;;;;;;kEAC9B,6LAAC,oIAAA,CAAA,YAAS;kEAAE,KAAK,YAAY,IAAI;;;;;;kEACjC,6LAAC,oIAAA,CAAA,YAAS;kEAAE,KAAK,SAAS,IAAI;;;;;;kEAC9B,6LAAC,oIAAA,CAAA,YAAS;kEACR,cAAA,6LAAC;4DAAI,WAAU;;gEACZ,uBAAuB,KAAK,YAAY;8EACzC,6LAAC;oEAAK,WAAU;8EACb,KAAK,YAAY,KAAK,IAAI,aAC1B,KAAK,YAAY,KAAK,IAAI,WAC1B,KAAK,YAAY,KAAK,IAAI,cAAc;;;;;;;;;;;;;;;;;kEAI/C,6LAAC,oIAAA,CAAA,YAAS;kEAAE,eAAe;;;;;;kEAC3B,6LAAC,oIAAA,CAAA,YAAS;kEAAE,gBAAgB;;;;;;kEAC5B,6LAAC,oIAAA,CAAA,YAAS;kEACR,cAAA,6LAAC,+IAAA,CAAA,eAAY;;8EACX,6LAAC,+IAAA,CAAA,sBAAmB;oEAAC,OAAO;8EAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAQ,MAAK;kFAC3B,cAAA,6LAAC,mNAAA,CAAA,iBAAc;4EAAC,WAAU;;;;;;;;;;;;;;;;8EAG9B,6LAAC,+IAAA,CAAA,sBAAmB;oEAAC,OAAM;;sFACzB,6LAAC,+IAAA,CAAA,mBAAgB;4EAAC,SAAS,IAAM,sBAAsB,MAAM;sFAAS;;;;;;sFAGtE,6LAAC,+IAAA,CAAA,mBAAgB;4EAAC,SAAS,IAAM,sBAAsB,MAAM;sFAAgB;;;;;;sFAG7E,6LAAC,+IAAA,CAAA,mBAAgB;4EAAC,SAAS,IAAM,sBAAsB,MAAM;sFAAkB;;;;;;sFAG/E,6LAAC,+IAAA,CAAA,mBAAgB;4EAAC,SAAS,IAAM,sBAAsB,MAAM;4EAAW,WAAU;sFAAe;;;;;;;;;;;;;;;;;;;;;;;;+CAhD1F,KAAK,OAAO;;;;;;;;;;;;;;;;4BA2DhC,aAAa,MAAM,KAAK,mBACvB,6LAAC;gCAAI,WAAU;0CAAyC;;;;;;;;;;;;;;;;;;;;;;;;AAQlE;GA7OwB;KAAA", "debugId": null}}, {"offset": {"line": 2355, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,6LAAC,qKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;MAhCS;AAkCT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 2553, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 2802, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cavi/InserisciMetriDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, AlertCircle } from 'lucide-react'\nimport { Cavo } from '@/types'\n\ninterface Bobina {\n  id_bobina: string\n  tipologia: string\n  formazione: string\n  metri_residui: number\n  fornitore?: string\n}\n\ninterface InserisciMetriDialogProps {\n  open: boolean\n  onClose: () => void\n  cavo: Cavo | null\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\nexport default function InserisciMetriDialog({\n  open,\n  onClose,\n  cavo,\n  onSuccess,\n  onError\n}: InserisciMetriDialogProps) {\n  const [metriPosati, setMetriPosati] = useState('')\n  const [selectedBobina, setSelectedBobina] = useState('')\n  const [bobine, setBobine] = useState<Bobina[]>([])\n  const [loading, setLoading] = useState(false)\n  const [loadingBobine, setLoadingBobine] = useState(false)\n  const [error, setError] = useState('')\n\n  // Carica bobine compatibili quando si apre il dialog\n  useEffect(() => {\n    if (open && cavo) {\n      loadBobineCompatibili()\n      setMetriPosati(cavo.metri_teorici?.toString() || '')\n      setSelectedBobina('')\n      setError('')\n    }\n  }, [open, cavo])\n\n  const loadBobineCompatibili = async () => {\n    if (!cavo) return\n\n    try {\n      setLoadingBobine(true)\n      \n      // Simula chiamata API per caricare bobine compatibili\n      // TODO: Sostituire con chiamata API reale\n      const mockBobine: Bobina[] = [\n        {\n          id_bobina: 'BOBINA_VUOTA',\n          tipologia: cavo.tipologia || '',\n          formazione: cavo.formazione || '',\n          metri_residui: 0\n        },\n        {\n          id_bobina: 'BOB001',\n          tipologia: cavo.tipologia || '',\n          formazione: cavo.formazione || '',\n          metri_residui: 500,\n          fornitore: 'Fornitore A'\n        },\n        {\n          id_bobina: 'BOB002',\n          tipologia: cavo.tipologia || '',\n          formazione: cavo.formazione || '',\n          metri_residui: 300,\n          fornitore: 'Fornitore B'\n        }\n      ]\n\n      setBobine(mockBobine)\n    } catch (error) {\n      console.error('Errore nel caricamento bobine:', error)\n      onError('Errore nel caricamento delle bobine disponibili')\n    } finally {\n      setLoadingBobine(false)\n    }\n  }\n\n  const handleSave = async () => {\n    if (!cavo || !metriPosati || !selectedBobina) {\n      setError('Compilare tutti i campi obbligatori')\n      return\n    }\n\n    const metri = parseFloat(metriPosati)\n    if (isNaN(metri) || metri <= 0) {\n      setError('Inserire un valore valido per i metri posati')\n      return\n    }\n\n    if (metri > (cavo.metri_teorici || 0)) {\n      setError('I metri posati non possono superare i metri teorici')\n      return\n    }\n\n    // Verifica metri disponibili nella bobina (se non è BOBINA_VUOTA)\n    const bobina = bobine.find(b => b.id_bobina === selectedBobina)\n    if (bobina && bobina.id_bobina !== 'BOBINA_VUOTA' && metri > bobina.metri_residui) {\n      setError(`La bobina selezionata ha solo ${bobina.metri_residui}m disponibili`)\n      return\n    }\n\n    try {\n      setLoading(true)\n      setError('')\n\n      // TODO: Sostituire con chiamata API reale\n      console.log('Aggiornamento metri posati:', {\n        cavoId: cavo.id_cavo,\n        metriPosati: metri,\n        bobinaId: selectedBobina\n      })\n\n      // Simula chiamata API\n      await new Promise(resolve => setTimeout(resolve, 1000))\n\n      onSuccess(`Metri posati aggiornati con successo per il cavo ${cavo.id_cavo}: ${metri}m`)\n      onClose()\n    } catch (error) {\n      console.error('Errore nel salvataggio:', error)\n      onError('Errore durante il salvataggio dei metri posati')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleClose = () => {\n    if (!loading) {\n      setMetriPosati('')\n      setSelectedBobina('')\n      setError('')\n      onClose()\n    }\n  }\n\n  if (!cavo) return null\n\n  return (\n    <Dialog open={open} onOpenChange={handleClose}>\n      <DialogContent className=\"sm:max-w-md\">\n        <DialogHeader>\n          <DialogTitle>Inserisci Metri Posati</DialogTitle>\n          <DialogDescription>\n            Inserisci i metri posati per il cavo {cavo.id_cavo}\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-4\">\n          {/* Informazioni cavo */}\n          <div className=\"p-3 bg-gray-50 rounded-lg\">\n            <div className=\"grid grid-cols-2 gap-2 text-sm\">\n              <div><strong>Sistema:</strong> {cavo.sistema}</div>\n              <div><strong>Utility:</strong> {cavo.utility}</div>\n              <div><strong>Tipologia:</strong> {cavo.tipologia}</div>\n              <div><strong>Formazione:</strong> {cavo.formazione}</div>\n              <div><strong>Da:</strong> {cavo.da}</div>\n              <div><strong>A:</strong> {cavo.a}</div>\n              <div className=\"col-span-2\">\n                <strong>Metri teorici:</strong> {cavo.metri_teorici}m\n              </div>\n            </div>\n          </div>\n\n          {/* Metri posati */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"metri\">Metri Posati *</Label>\n            <Input\n              id=\"metri\"\n              type=\"number\"\n              step=\"0.1\"\n              min=\"0\"\n              max={cavo.metri_teorici || 0}\n              value={metriPosati}\n              onChange={(e) => setMetriPosati(e.target.value)}\n              placeholder=\"Inserisci metri posati\"\n            />\n          </div>\n\n          {/* Selezione bobina */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"bobina\">Bobina *</Label>\n            {loadingBobine ? (\n              <div className=\"flex items-center space-x-2 p-2\">\n                <Loader2 className=\"h-4 w-4 animate-spin\" />\n                <span className=\"text-sm\">Caricamento bobine...</span>\n              </div>\n            ) : (\n              <Select value={selectedBobina} onValueChange={setSelectedBobina}>\n                <SelectTrigger>\n                  <SelectValue placeholder=\"Seleziona bobina\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {bobine.map((bobina) => (\n                    <SelectItem key={bobina.id_bobina} value={bobina.id_bobina}>\n                      <div className=\"flex flex-col\">\n                        <span>{bobina.id_bobina}</span>\n                        {bobina.id_bobina !== 'BOBINA_VUOTA' && (\n                          <span className=\"text-xs text-muted-foreground\">\n                            {bobina.metri_residui}m disponibili\n                            {bobina.fornitore && ` - ${bobina.fornitore}`}\n                          </span>\n                        )}\n                      </div>\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            )}\n          </div>\n\n          {/* Errori */}\n          {error && (\n            <Alert variant=\"destructive\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>{error}</AlertDescription>\n            </Alert>\n          )}\n        </div>\n\n        <DialogFooter>\n          <Button variant=\"outline\" onClick={handleClose} disabled={loading}>\n            Annulla\n          </Button>\n          <Button \n            onClick={handleSave} \n            disabled={loading || !metriPosati || !selectedBobina}\n          >\n            {loading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n            {loading ? 'Salvando...' : 'Salva'}\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AAOA;AACA;AAAA;;;AAtBA;;;;;;;;;AAyCe,SAAS,qBAAqB,EAC3C,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,SAAS,EACT,OAAO,EACmB;;IAC1B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,qDAAqD;IACrD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,QAAQ,MAAM;gBAChB;gBACA,eAAe,KAAK,aAAa,EAAE,cAAc;gBACjD,kBAAkB;gBAClB,SAAS;YACX;QACF;yCAAG;QAAC;QAAM;KAAK;IAEf,MAAM,wBAAwB;QAC5B,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,iBAAiB;YAEjB,sDAAsD;YACtD,0CAA0C;YAC1C,MAAM,aAAuB;gBAC3B;oBACE,WAAW;oBACX,WAAW,KAAK,SAAS,IAAI;oBAC7B,YAAY,KAAK,UAAU,IAAI;oBAC/B,eAAe;gBACjB;gBACA;oBACE,WAAW;oBACX,WAAW,KAAK,SAAS,IAAI;oBAC7B,YAAY,KAAK,UAAU,IAAI;oBAC/B,eAAe;oBACf,WAAW;gBACb;gBACA;oBACE,WAAW;oBACX,WAAW,KAAK,SAAS,IAAI;oBAC7B,YAAY,KAAK,UAAU,IAAI;oBAC/B,eAAe;oBACf,WAAW;gBACb;aACD;YAED,UAAU;QACZ,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,QAAQ;QACV,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,gBAAgB;YAC5C,SAAS;YACT;QACF;QAEA,MAAM,QAAQ,WAAW;QACzB,IAAI,MAAM,UAAU,SAAS,GAAG;YAC9B,SAAS;YACT;QACF;QAEA,IAAI,QAAQ,CAAC,KAAK,aAAa,IAAI,CAAC,GAAG;YACrC,SAAS;YACT;QACF;QAEA,kEAAkE;QAClE,MAAM,SAAS,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK;QAChD,IAAI,UAAU,OAAO,SAAS,KAAK,kBAAkB,QAAQ,OAAO,aAAa,EAAE;YACjF,SAAS,CAAC,8BAA8B,EAAE,OAAO,aAAa,CAAC,aAAa,CAAC;YAC7E;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YAET,0CAA0C;YAC1C,QAAQ,GAAG,CAAC,+BAA+B;gBACzC,QAAQ,KAAK,OAAO;gBACpB,aAAa;gBACb,UAAU;YACZ;YAEA,sBAAsB;YACtB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,UAAU,CAAC,iDAAiD,EAAE,KAAK,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;YACvF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;YACZ,eAAe;YACf,kBAAkB;YAClB,SAAS;YACT;QACF;IACF;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,cAAW;sCAAC;;;;;;sCACb,6LAAC,qIAAA,CAAA,oBAAiB;;gCAAC;gCACqB,KAAK,OAAO;;;;;;;;;;;;;8BAItD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DAAI,6LAAC;0DAAO;;;;;;4CAAiB;4CAAE,KAAK,OAAO;;;;;;;kDAC5C,6LAAC;;0DAAI,6LAAC;0DAAO;;;;;;4CAAiB;4CAAE,KAAK,OAAO;;;;;;;kDAC5C,6LAAC;;0DAAI,6LAAC;0DAAO;;;;;;4CAAmB;4CAAE,KAAK,SAAS;;;;;;;kDAChD,6LAAC;;0DAAI,6LAAC;0DAAO;;;;;;4CAAoB;4CAAE,KAAK,UAAU;;;;;;;kDAClD,6LAAC;;0DAAI,6LAAC;0DAAO;;;;;;4CAAY;4CAAE,KAAK,EAAE;;;;;;;kDAClC,6LAAC;;0DAAI,6LAAC;0DAAO;;;;;;4CAAW;4CAAE,KAAK,CAAC;;;;;;;kDAChC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAO;;;;;;4CAAuB;4CAAE,KAAK,aAAa;4CAAC;;;;;;;;;;;;;;;;;;sCAM1D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAQ;;;;;;8CACvB,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,MAAK;oCACL,MAAK;oCACL,KAAI;oCACJ,KAAK,KAAK,aAAa,IAAI;oCAC3B,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,aAAY;;;;;;;;;;;;sCAKhB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAS;;;;;;gCACvB,8BACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;yDAG5B,6LAAC,qIAAA,CAAA,SAAM;oCAAC,OAAO;oCAAgB,eAAe;;sDAC5C,6LAAC,qIAAA,CAAA,gBAAa;sDACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,6LAAC,qIAAA,CAAA,gBAAa;sDACX,OAAO,GAAG,CAAC,CAAC,uBACX,6LAAC,qIAAA,CAAA,aAAU;oDAAwB,OAAO,OAAO,SAAS;8DACxD,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAM,OAAO,SAAS;;;;;;4DACtB,OAAO,SAAS,KAAK,gCACpB,6LAAC;gEAAK,WAAU;;oEACb,OAAO,aAAa;oEAAC;oEACrB,OAAO,SAAS,IAAI,CAAC,GAAG,EAAE,OAAO,SAAS,EAAE;;;;;;;;;;;;;mDANpC,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;wBAkB1C,uBACC,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAQ;;8CACb,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC,oIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;;;;;;;8BAKzB,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAa,UAAU;sCAAS;;;;;;sCAGnE,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,WAAW,CAAC,eAAe,CAAC;;gCAErC,yBAAW,6LAAC,oNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAC9B,UAAU,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;AAMvC;GA5NwB;KAAA", "debugId": null}}, {"offset": {"line": 3329, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cavi/ModificaBobinaDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Label } from '@/components/ui/label'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, AlertCircle, Package } from 'lucide-react'\nimport { Cavo } from '@/types'\n\ninterface Bobina {\n  id_bobina: string\n  tipologia: string\n  formazione: string\n  metri_residui: number\n  fornitore?: string\n}\n\ninterface ModificaBobinaDialogProps {\n  open: boolean\n  onClose: () => void\n  cavo: Cavo | null\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\nexport default function ModificaBobinaDialog({\n  open,\n  onClose,\n  cavo,\n  onSuccess,\n  onError\n}: ModificaBobinaDialogProps) {\n  const [selectedBobina, setSelectedBobina] = useState('')\n  const [bobine, setBobine] = useState<Bobina[]>([])\n  const [loading, setLoading] = useState(false)\n  const [loadingBobine, setLoadingBobine] = useState(false)\n  const [error, setError] = useState('')\n\n  // Carica bobine compatibili quando si apre il dialog\n  useEffect(() => {\n    if (open && cavo) {\n      loadBobineCompatibili()\n      setSelectedBobina(cavo.id_bobina || '')\n      setError('')\n    }\n  }, [open, cavo])\n\n  const loadBobineCompatibili = async () => {\n    if (!cavo) return\n\n    try {\n      setLoadingBobine(true)\n      \n      // Simula chiamata API per caricare bobine compatibili\n      // TODO: Sostituire con chiamata API reale\n      const mockBobine: Bobina[] = [\n        {\n          id_bobina: 'BOBINA_VUOTA',\n          tipologia: cavo.tipologia || '',\n          formazione: cavo.formazione || '',\n          metri_residui: 0\n        },\n        {\n          id_bobina: 'BOB001',\n          tipologia: cavo.tipologia || '',\n          formazione: cavo.formazione || '',\n          metri_residui: 500,\n          fornitore: 'Fornitore A'\n        },\n        {\n          id_bobina: 'BOB002',\n          tipologia: cavo.tipologia || '',\n          formazione: cavo.formazione || '',\n          metri_residui: 300,\n          fornitore: 'Fornitore B'\n        },\n        {\n          id_bobina: 'BOB003',\n          tipologia: cavo.tipologia || '',\n          formazione: cavo.formazione || '',\n          metri_residui: 150,\n          fornitore: 'Fornitore C'\n        }\n      ]\n\n      // Se il cavo ha già una bobina, includila nella lista\n      if (cavo.id_bobina && !mockBobine.find(b => b.id_bobina === cavo.id_bobina)) {\n        mockBobine.push({\n          id_bobina: cavo.id_bobina,\n          tipologia: cavo.tipologia || '',\n          formazione: cavo.formazione || '',\n          metri_residui: 0 // Bobina attualmente in uso\n        })\n      }\n\n      setBobine(mockBobine)\n    } catch (error) {\n      console.error('Errore nel caricamento bobine:', error)\n      onError('Errore nel caricamento delle bobine disponibili')\n    } finally {\n      setLoadingBobine(false)\n    }\n  }\n\n  const handleSave = async () => {\n    if (!cavo || !selectedBobina) {\n      setError('Selezionare una bobina')\n      return\n    }\n\n    if (selectedBobina === cavo.id_bobina) {\n      setError('La bobina selezionata è già associata al cavo')\n      return\n    }\n\n    // Verifica che la bobina abbia metri sufficienti (se non è BOBINA_VUOTA)\n    const bobina = bobine.find(b => b.id_bobina === selectedBobina)\n    if (bobina && bobina.id_bobina !== 'BOBINA_VUOTA' && cavo.metri_posati > bobina.metri_residui) {\n      setError(`La bobina selezionata ha solo ${bobina.metri_residui}m disponibili, ma il cavo ha ${cavo.metri_posati}m posati`)\n      return\n    }\n\n    try {\n      setLoading(true)\n      setError('')\n\n      // TODO: Sostituire con chiamata API reale\n      console.log('Modifica bobina:', {\n        cavoId: cavo.id_cavo,\n        nuovaBobina: selectedBobina,\n        vecchiaBobina: cavo.id_bobina\n      })\n\n      // Simula chiamata API\n      await new Promise(resolve => setTimeout(resolve, 1000))\n\n      const message = selectedBobina === 'BOBINA_VUOTA' \n        ? `Bobina vuota assegnata al cavo ${cavo.id_cavo}`\n        : `Bobina ${selectedBobina} assegnata al cavo ${cavo.id_cavo}`\n      \n      onSuccess(message)\n      onClose()\n    } catch (error) {\n      console.error('Errore nel salvataggio:', error)\n      onError('Errore durante la modifica della bobina')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleClose = () => {\n    if (!loading) {\n      setSelectedBobina('')\n      setError('')\n      onClose()\n    }\n  }\n\n  if (!cavo) return null\n\n  return (\n    <Dialog open={open} onOpenChange={handleClose}>\n      <DialogContent className=\"sm:max-w-md\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center space-x-2\">\n            <Package className=\"h-5 w-5\" />\n            <span>Modifica Bobina</span>\n          </DialogTitle>\n          <DialogDescription>\n            Modifica la bobina associata al cavo {cavo.id_cavo}\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-4\">\n          {/* Informazioni cavo */}\n          <div className=\"p-3 bg-gray-50 rounded-lg\">\n            <div className=\"grid grid-cols-2 gap-2 text-sm\">\n              <div><strong>Sistema:</strong> {cavo.sistema}</div>\n              <div><strong>Utility:</strong> {cavo.utility}</div>\n              <div><strong>Tipologia:</strong> {cavo.tipologia}</div>\n              <div><strong>Formazione:</strong> {cavo.formazione}</div>\n              <div><strong>Metri posati:</strong> {cavo.metri_posati || 0}m</div>\n              <div><strong>Bobina attuale:</strong> {cavo.id_bobina || 'Nessuna'}</div>\n            </div>\n          </div>\n\n          {/* Selezione nuova bobina */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"bobina\">Nuova Bobina *</Label>\n            {loadingBobine ? (\n              <div className=\"flex items-center space-x-2 p-2\">\n                <Loader2 className=\"h-4 w-4 animate-spin\" />\n                <span className=\"text-sm\">Caricamento bobine...</span>\n              </div>\n            ) : (\n              <Select value={selectedBobina} onValueChange={setSelectedBobina}>\n                <SelectTrigger>\n                  <SelectValue placeholder=\"Seleziona nuova bobina\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {bobine.map((bobina) => (\n                    <SelectItem \n                      key={bobina.id_bobina} \n                      value={bobina.id_bobina}\n                      disabled={bobina.id_bobina === cavo.id_bobina}\n                    >\n                      <div className=\"flex flex-col\">\n                        <div className=\"flex items-center space-x-2\">\n                          <span>{bobina.id_bobina}</span>\n                          {bobina.id_bobina === cavo.id_bobina && (\n                            <span className=\"text-xs bg-blue-100 text-blue-800 px-1 rounded\">\n                              Attuale\n                            </span>\n                          )}\n                        </div>\n                        {bobina.id_bobina !== 'BOBINA_VUOTA' && (\n                          <span className=\"text-xs text-muted-foreground\">\n                            {bobina.metri_residui}m disponibili\n                            {bobina.fornitore && ` - ${bobina.fornitore}`}\n                          </span>\n                        )}\n                      </div>\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            )}\n          </div>\n\n          {/* Avviso per bobina vuota */}\n          {selectedBobina === 'BOBINA_VUOTA' && (\n            <Alert>\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>\n                Stai assegnando una bobina vuota. Questo permetterà di posare il cavo \n                e associare la bobina reale in un secondo momento.\n              </AlertDescription>\n            </Alert>\n          )}\n\n          {/* Errori */}\n          {error && (\n            <Alert variant=\"destructive\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>{error}</AlertDescription>\n            </Alert>\n          )}\n        </div>\n\n        <DialogFooter>\n          <Button variant=\"outline\" onClick={handleClose} disabled={loading}>\n            Annulla\n          </Button>\n          <Button \n            onClick={handleSave} \n            disabled={loading || !selectedBobina || selectedBobina === cavo.id_bobina}\n          >\n            {loading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n            {loading ? 'Salvando...' : 'Salva'}\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AAOA;AACA;AAAA;AAAA;;;AArBA;;;;;;;;AAwCe,SAAS,qBAAqB,EAC3C,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,SAAS,EACT,OAAO,EACmB;;IAC1B,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,qDAAqD;IACrD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,QAAQ,MAAM;gBAChB;gBACA,kBAAkB,KAAK,SAAS,IAAI;gBACpC,SAAS;YACX;QACF;yCAAG;QAAC;QAAM;KAAK;IAEf,MAAM,wBAAwB;QAC5B,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,iBAAiB;YAEjB,sDAAsD;YACtD,0CAA0C;YAC1C,MAAM,aAAuB;gBAC3B;oBACE,WAAW;oBACX,WAAW,KAAK,SAAS,IAAI;oBAC7B,YAAY,KAAK,UAAU,IAAI;oBAC/B,eAAe;gBACjB;gBACA;oBACE,WAAW;oBACX,WAAW,KAAK,SAAS,IAAI;oBAC7B,YAAY,KAAK,UAAU,IAAI;oBAC/B,eAAe;oBACf,WAAW;gBACb;gBACA;oBACE,WAAW;oBACX,WAAW,KAAK,SAAS,IAAI;oBAC7B,YAAY,KAAK,UAAU,IAAI;oBAC/B,eAAe;oBACf,WAAW;gBACb;gBACA;oBACE,WAAW;oBACX,WAAW,KAAK,SAAS,IAAI;oBAC7B,YAAY,KAAK,UAAU,IAAI;oBAC/B,eAAe;oBACf,WAAW;gBACb;aACD;YAED,sDAAsD;YACtD,IAAI,KAAK,SAAS,IAAI,CAAC,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK,KAAK,SAAS,GAAG;gBAC3E,WAAW,IAAI,CAAC;oBACd,WAAW,KAAK,SAAS;oBACzB,WAAW,KAAK,SAAS,IAAI;oBAC7B,YAAY,KAAK,UAAU,IAAI;oBAC/B,eAAe,EAAE,4BAA4B;gBAC/C;YACF;YAEA,UAAU;QACZ,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,QAAQ;QACV,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,QAAQ,CAAC,gBAAgB;YAC5B,SAAS;YACT;QACF;QAEA,IAAI,mBAAmB,KAAK,SAAS,EAAE;YACrC,SAAS;YACT;QACF;QAEA,yEAAyE;QACzE,MAAM,SAAS,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK;QAChD,IAAI,UAAU,OAAO,SAAS,KAAK,kBAAkB,KAAK,YAAY,GAAG,OAAO,aAAa,EAAE;YAC7F,SAAS,CAAC,8BAA8B,EAAE,OAAO,aAAa,CAAC,6BAA6B,EAAE,KAAK,YAAY,CAAC,QAAQ,CAAC;YACzH;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YAET,0CAA0C;YAC1C,QAAQ,GAAG,CAAC,oBAAoB;gBAC9B,QAAQ,KAAK,OAAO;gBACpB,aAAa;gBACb,eAAe,KAAK,SAAS;YAC/B;YAEA,sBAAsB;YACtB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,UAAU,mBAAmB,iBAC/B,CAAC,+BAA+B,EAAE,KAAK,OAAO,EAAE,GAChD,CAAC,OAAO,EAAE,eAAe,mBAAmB,EAAE,KAAK,OAAO,EAAE;YAEhE,UAAU;YACV;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;YACZ,kBAAkB;YAClB,SAAS;YACT;QACF;IACF;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC,2MAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,6LAAC;8CAAK;;;;;;;;;;;;sCAER,6LAAC,qIAAA,CAAA,oBAAiB;;gCAAC;gCACqB,KAAK,OAAO;;;;;;;;;;;;;8BAItD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DAAI,6LAAC;0DAAO;;;;;;4CAAiB;4CAAE,KAAK,OAAO;;;;;;;kDAC5C,6LAAC;;0DAAI,6LAAC;0DAAO;;;;;;4CAAiB;4CAAE,KAAK,OAAO;;;;;;;kDAC5C,6LAAC;;0DAAI,6LAAC;0DAAO;;;;;;4CAAmB;4CAAE,KAAK,SAAS;;;;;;;kDAChD,6LAAC;;0DAAI,6LAAC;0DAAO;;;;;;4CAAoB;4CAAE,KAAK,UAAU;;;;;;;kDAClD,6LAAC;;0DAAI,6LAAC;0DAAO;;;;;;4CAAsB;4CAAE,KAAK,YAAY,IAAI;4CAAE;;;;;;;kDAC5D,6LAAC;;0DAAI,6LAAC;0DAAO;;;;;;4CAAwB;4CAAE,KAAK,SAAS,IAAI;;;;;;;;;;;;;;;;;;sCAK7D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAS;;;;;;gCACvB,8BACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;yDAG5B,6LAAC,qIAAA,CAAA,SAAM;oCAAC,OAAO;oCAAgB,eAAe;;sDAC5C,6LAAC,qIAAA,CAAA,gBAAa;sDACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,6LAAC,qIAAA,CAAA,gBAAa;sDACX,OAAO,GAAG,CAAC,CAAC,uBACX,6LAAC,qIAAA,CAAA,aAAU;oDAET,OAAO,OAAO,SAAS;oDACvB,UAAU,OAAO,SAAS,KAAK,KAAK,SAAS;8DAE7C,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;kFAAM,OAAO,SAAS;;;;;;oEACtB,OAAO,SAAS,KAAK,KAAK,SAAS,kBAClC,6LAAC;wEAAK,WAAU;kFAAiD;;;;;;;;;;;;4DAKpE,OAAO,SAAS,KAAK,gCACpB,6LAAC;gEAAK,WAAU;;oEACb,OAAO,aAAa;oEAAC;oEACrB,OAAO,SAAS,IAAI,CAAC,GAAG,EAAE,OAAO,SAAS,EAAE;;;;;;;;;;;;;mDAhB9C,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;wBA4BhC,mBAAmB,gCAClB,6LAAC,oIAAA,CAAA,QAAK;;8CACJ,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC,oIAAA,CAAA,mBAAgB;8CAAC;;;;;;;;;;;;wBAQrB,uBACC,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAQ;;8CACb,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC,oIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;;;;;;;8BAKzB,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAa,UAAU;sCAAS;;;;;;sCAGnE,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,WAAW,CAAC,kBAAkB,mBAAmB,KAAK,SAAS;;gCAExE,yBAAW,6LAAC,oNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAC9B,UAAU,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;AAMvC;GA/OwB;KAAA", "debugId": null}}, {"offset": {"line": 3871, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/app/cavi/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { caviApi } from '@/lib/api'\nimport { Cavo } from '@/types'\nimport CaviTable from '@/components/cavi/CaviTable'\nimport InserisciMetriDialog from '@/components/cavi/InserisciMetriDialog'\nimport ModificaBobinaDialog from '@/components/cavi/ModificaBobinaDialog'\nimport { useToast } from '@/hooks/use-toast'\nimport {\n  Cable,\n  Package,\n  CheckCircle,\n  AlertCircle,\n  Clock,\n  TrendingUp,\n  Activity,\n  Loader2,\n  BarChart3,\n  Zap\n} from 'lucide-react'\n\ninterface DashboardStats {\n  totali: number\n  installati: number\n  collegati: number\n  certificati: number\n  percentualeInstallazione: number\n  percentualeCollegamento: number\n  percentualeCertificazione: number\n  metriTotali: number\n  metriInstallati: number\n  metriCollegati: number\n  metriCertificati: number\n}\n\nexport default function CaviPage() {\n  const { user, cantiere, isAuthenticated, isLoading } = useAuth()\n  const router = useRouter()\n  const { toast } = useToast()\n  const [cavi, setCavi] = useState<Cavo[]>([])\n  const [caviSpare, setCaviSpare] = useState<Cavo[]>([])\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState('')\n  const [selectedCavi, setSelectedCavi] = useState<number[]>([])\n  const [selectionEnabled, setSelectionEnabled] = useState(false)\n\n  // Stati per i dialoghi\n  const [inserisciMetriDialog, setInserisciMetriDialog] = useState<{\n    open: boolean\n    cavo: Cavo | null\n  }>({ open: false, cavo: null })\n\n  const [modificaBobinaDialog, setModificaBobinaDialog] = useState<{\n    open: boolean\n    cavo: Cavo | null\n  }>({ open: false, cavo: null })\n  const [stats, setStats] = useState<DashboardStats>({\n    totali: 0,\n    installati: 0,\n    collegati: 0,\n    certificati: 0,\n    percentualeInstallazione: 0,\n    percentualeCollegamento: 0,\n    percentualeCertificazione: 0,\n    metriTotali: 0,\n    metriInstallati: 0,\n    metriCollegati: 0,\n    metriCertificati: 0\n  })\n\n  // Get cantiere ID\n  const [cantiereId, setCantiereId] = useState<number>(0)\n\n  useEffect(() => {\n    if (!isLoading && !isAuthenticated) {\n      router.push('/login')\n    }\n  }, [isAuthenticated, isLoading, router])\n\n  useEffect(() => {\n    if (typeof window !== 'undefined') {\n      const storedId = cantiere?.id_cantiere || parseInt(localStorage.getItem('selectedCantiereId') || '0')\n      setCantiereId(storedId)\n    }\n  }, [cantiere])\n\n  // Carica i cavi dal backend\n  useEffect(() => {\n    if (cantiereId && cantiereId > 0) {\n      loadCavi()\n    }\n  }, [cantiereId])\n\n  const loadCavi = async () => {\n    try {\n      setLoading(true)\n      setError('')\n\n      console.log('🔍 Tentativo caricamento cavi per cantiere:', cantiereId)\n      console.log('🔍 Token presente:', !!localStorage.getItem('token'))\n\n      // Prima prova con l'API normale\n      try {\n        const data = await caviApi.getCavi(cantiereId)\n        console.log('✅ API normale riuscita, cavi ricevuti:', data?.length || 0)\n\n        // Separa cavi attivi e spare\n        const caviAttivi = data.filter((cavo: Cavo) => !cavo.spare)\n        const caviSpareFiltered = data.filter((cavo: Cavo) => cavo.spare)\n\n        setCavi(caviAttivi)\n        setCaviSpare(caviSpareFiltered)\n\n        // Calcola statistiche\n        calculateStats(caviAttivi)\n\n      } catch (apiError: any) {\n        console.log('❌ API normale fallita, provo endpoint debug...')\n        console.error('Errore API normale:', apiError)\n\n        // Fallback: prova con endpoint debug (senza autenticazione)\n        try {\n          const response = await fetch(`http://localhost:8001/api/cavi/debug/${cantiereId}`)\n          const debugData = await response.json()\n          console.log('✅ Endpoint debug riuscito:', debugData)\n\n          if (debugData.cavi && Array.isArray(debugData.cavi)) {\n            const caviAttivi = debugData.cavi.filter((cavo: any) => !cavo.spare)\n            const caviSpareFiltered = debugData.cavi.filter((cavo: any) => cavo.spare)\n\n            setCavi(caviAttivi)\n            setCaviSpare(caviSpareFiltered)\n            calculateStats(caviAttivi)\n\n            setError('⚠️ Dati caricati tramite endpoint debug (problema autenticazione)')\n          } else {\n            throw new Error('Formato dati debug non valido')\n          }\n        } catch (debugError) {\n          console.error('❌ Anche endpoint debug fallito:', debugError)\n          throw apiError // Rilancia l'errore originale\n        }\n      }\n\n    } catch (error: any) {\n      console.error('❌ Errore generale nel caricamento cavi:', error)\n      setError(`Errore nel caricamento dei cavi: ${error.response?.data?.detail || error.message}`)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const calculateStats = (caviData: Cavo[]) => {\n    const totali = caviData.length\n    const installati = caviData.filter(c => c.metri_posati > 0).length\n    const collegati = caviData.filter(c => c.collegamento === 3).length // 3 = collegato\n    const certificati = caviData.filter(c => c.certificato).length\n\n    const metriTotali = caviData.reduce((sum, c) => sum + (c.metri_teorici || 0), 0)\n    const metriInstallati = caviData.reduce((sum, c) => sum + (c.metri_posati || 0), 0)\n    const metriCollegati = caviData.filter(c => c.collegamento === 3).reduce((sum, c) => sum + (c.metri_posati || 0), 0)\n    const metriCertificati = caviData.filter(c => c.certificato).reduce((sum, c) => sum + (c.metri_posati || 0), 0)\n\n    setStats({\n      totali,\n      installati,\n      collegati,\n      certificati,\n      percentualeInstallazione: totali > 0 ? Math.round((installati / totali) * 100) : 0,\n      percentualeCollegamento: totali > 0 ? Math.round((collegati / totali) * 100) : 0,\n      percentualeCertificazione: totali > 0 ? Math.round((certificati / totali) * 100) : 0,\n      metriTotali,\n      metriInstallati,\n      metriCollegati,\n      metriCertificati\n    })\n  }\n\n  // Gestione azioni sui cavi\n  const handleStatusAction = (cavo: Cavo, action: string) => {\n    console.log('Status action:', action, 'for cavo:', cavo.id_cavo)\n\n    switch (action) {\n      case 'insert_meters':\n        setInserisciMetriDialog({ open: true, cavo })\n        break\n      case 'modify_reel':\n        setModificaBobinaDialog({ open: true, cavo })\n        break\n    }\n  }\n\n  const handleContextMenuAction = (cavo: Cavo, action: string) => {\n    console.log('Context menu action:', action, 'for cavo:', cavo.id_cavo)\n\n    switch (action) {\n      case 'edit':\n        toast({\n          title: \"Funzione in sviluppo\",\n          description: \"La modifica del cavo sarà disponibile presto\",\n        })\n        break\n      case 'connections':\n        toast({\n          title: \"Funzione in sviluppo\",\n          description: \"La gestione collegamenti sarà disponibile presto\",\n        })\n        break\n      case 'certification':\n        toast({\n          title: \"Funzione in sviluppo\",\n          description: \"La certificazione sarà disponibile presto\",\n        })\n        break\n      case 'delete':\n        toast({\n          title: \"Funzione in sviluppo\",\n          description: \"L'eliminazione del cavo sarà disponibile presto\",\n          variant: \"destructive\"\n        })\n        break\n    }\n  }\n\n  // Gestione successo/errore dialoghi\n  const handleDialogSuccess = (message: string) => {\n    toast({\n      title: \"Operazione completata\",\n      description: message,\n    })\n    // Ricarica i dati\n    loadCavi()\n  }\n\n  const handleDialogError = (message: string) => {\n    toast({\n      title: \"Errore\",\n      description: message,\n      variant: \"destructive\"\n    })\n  }\n\n  if (isLoading || loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <Loader2 className=\"h-8 w-8 animate-spin\" />\n      </div>\n    )\n  }\n\n  if (!cantiereId) {\n    return (\n      <div className=\"container mx-auto p-6\">\n        <Alert>\n          <AlertCircle className=\"h-4 w-4\" />\n          <AlertDescription>\n            Nessun cantiere selezionato. Seleziona un cantiere per visualizzare i cavi.\n          </AlertDescription>\n        </Alert>\n        <div className=\"mt-4 p-4 bg-gray-100 rounded\">\n          <h3 className=\"font-bold\">Debug Info:</h3>\n          <p>User: {user ? user.username : 'Non autenticato'}</p>\n          <p>Cantiere context: {cantiere ? cantiere.commessa : 'Nessuno'}</p>\n          <p>Token presente: {typeof window !== 'undefined' ? (localStorage.getItem('token') ? 'Sì' : 'No') : 'N/A'}</p>\n        </div>\n      </div>\n    )\n  }\n\n  if (error) {\n    return (\n      <div className=\"container mx-auto p-6\">\n        <Alert variant=\"destructive\">\n          <AlertCircle className=\"h-4 w-4\" />\n          <AlertDescription>{error}</AlertDescription>\n        </Alert>\n        <Button onClick={loadCavi} className=\"mt-4\">\n          Riprova\n        </Button>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"container mx-auto p-6\">\n\n      {/* Dashboard compatto con statistiche essenziali */}\n      <Card className=\"mb-6 bg-gray-50\">\n        <CardContent className=\"p-4\">\n          <div className=\"flex flex-wrap items-center justify-between gap-4\">\n            {/* Totale Cavi */}\n            <div className=\"flex items-center space-x-2\">\n              <Cable className=\"h-4 w-4 text-blue-600\" />\n              <div>\n                <div className=\"text-lg font-bold\">{stats.totali}</div>\n                <div className=\"text-xs text-muted-foreground\">Totale</div>\n              </div>\n            </div>\n\n            {/* Installazione */}\n            <div className=\"flex items-center space-x-2\">\n              <Activity className=\"h-4 w-4 text-green-600\" />\n              <div>\n                <div className=\"text-lg font-bold\">{stats.installati}</div>\n                <div className=\"text-xs text-muted-foreground\">\n                  Installati ({stats.percentualeInstallazione}%)\n                </div>\n              </div>\n            </div>\n\n            {/* Collegamento */}\n            <div className=\"flex items-center space-x-2\">\n              <Zap className=\"h-4 w-4 text-orange-600\" />\n              <div>\n                <div className=\"text-lg font-bold\">{stats.collegati}</div>\n                <div className=\"text-xs text-muted-foreground\">\n                  Collegati ({stats.percentualeCollegamento}%)\n                </div>\n              </div>\n            </div>\n\n            {/* Certificazione */}\n            <div className=\"flex items-center space-x-2\">\n              <CheckCircle className=\"h-4 w-4 text-purple-600\" />\n              <div>\n                <div className=\"text-lg font-bold\">{stats.certificati}</div>\n                <div className=\"text-xs text-muted-foreground\">\n                  Certificati ({stats.percentualeCertificazione}%)\n                </div>\n              </div>\n            </div>\n\n            {/* Metri */}\n            <div className=\"flex items-center space-x-2\">\n              <BarChart3 className=\"h-4 w-4 text-indigo-600\" />\n              <div>\n                <div className=\"text-lg font-bold\">{stats.metriInstallati.toLocaleString()}</div>\n                <div className=\"text-xs text-muted-foreground\">\n                  di {stats.metriTotali.toLocaleString()}m\n                </div>\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Tabella Cavi Attivi */}\n      <div className=\"mb-8\">\n        <CaviTable\n          cavi={cavi}\n          loading={loading}\n          selectionEnabled={selectionEnabled}\n          selectedCavi={selectedCavi}\n          onSelectionChange={setSelectedCavi}\n          onStatusAction={handleStatusAction}\n          onContextMenuAction={handleContextMenuAction}\n        />\n      </div>\n\n      {/* Tabella Cavi Spare */}\n      {caviSpare.length > 0 && (\n        <div className=\"mb-8\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center space-x-2\">\n                <Package className=\"h-5 w-5\" />\n                <span>Cavi Spare ({caviSpare.length})</span>\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <CaviTable\n                cavi={caviSpare}\n                loading={loading}\n                selectionEnabled={false}\n                onStatusAction={handleStatusAction}\n                onContextMenuAction={handleContextMenuAction}\n              />\n            </CardContent>\n          </Card>\n        </div>\n      )}\n\n      {/* Debug info - solo in development */}\n      {process.env.NODE_ENV === 'development' && (\n        <Card className=\"mt-6\">\n          <CardHeader>\n            <CardTitle>Debug Info</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-2 gap-4 text-sm\">\n              <div>\n                <p><strong>User:</strong> {user ? user.username : 'Non autenticato'}</p>\n                <p><strong>User Role:</strong> {user ? user.ruolo : 'N/A'}</p>\n                <p><strong>Cantiere ID:</strong> {cantiereId}</p>\n                <p><strong>Cantiere context:</strong> {cantiere ? cantiere.commessa : 'Nessuno'}</p>\n              </div>\n              <div>\n                <p><strong>Token presente:</strong> {typeof window !== 'undefined' ? (localStorage.getItem('token') ? 'Sì' : 'No') : 'N/A'}</p>\n                <p><strong>Loading:</strong> {loading ? 'Sì' : 'No'}</p>\n                <p><strong>Error:</strong> {error || 'Nessuno'}</p>\n                <p><strong>Cavi ricevuti:</strong> {cavi.length}</p>\n                <p><strong>Cavi spare:</strong> {caviSpare.length}</p>\n              </div>\n            </div>\n            <div className=\"mt-4\">\n              <Button\n                onClick={async () => {\n                  try {\n                    const response = await fetch('http://localhost:8001/api/cavi/debug/1')\n                    const data = await response.json()\n                    console.log('🔍 Test diretto backend:', data)\n                    alert(`Backend ha ${data.total_cavi} cavi per cantiere 1`)\n                  } catch (err) {\n                    console.error('❌ Errore test backend:', err)\n                    alert('Errore nel test backend')\n                  }\n                }}\n                variant=\"outline\"\n                size=\"sm\"\n              >\n                Test Backend Diretto\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Dialoghi */}\n      <InserisciMetriDialog\n        open={inserisciMetriDialog.open}\n        onClose={() => setInserisciMetriDialog({ open: false, cavo: null })}\n        cavo={inserisciMetriDialog.cavo}\n        onSuccess={handleDialogSuccess}\n        onError={handleDialogError}\n      />\n\n      <ModificaBobinaDialog\n        open={modificaBobinaDialog.open}\n        onClose={() => setModificaBobinaDialog({ open: false, cavo: null })}\n        cavo={modificaBobinaDialog.cavo}\n        onSuccess={handleDialogSuccess}\n        onError={handleDialogError}\n      />\n    </div>\n  )\n}\n\n\n"], "names": [], "mappings": ";;;AAsYO;;AApYP;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAfA;;;;;;;;;;;;;AA0Ce,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC7D,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,uBAAuB;IACvB,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAG5D;QAAE,MAAM;QAAO,MAAM;IAAK;IAE7B,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAG5D;QAAE,MAAM;QAAO,MAAM;IAAK;IAC7B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;QACjD,QAAQ;QACR,YAAY;QACZ,WAAW;QACX,aAAa;QACb,0BAA0B;QAC1B,yBAAyB;QACzB,2BAA2B;QAC3B,aAAa;QACb,iBAAiB;QACjB,gBAAgB;QAChB,kBAAkB;IACpB;IAEA,kBAAkB;IAClB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAErD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,CAAC,aAAa,CAAC,iBAAiB;gBAClC,OAAO,IAAI,CAAC;YACd;QACF;6BAAG;QAAC;QAAiB;QAAW;KAAO;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,wCAAmC;gBACjC,MAAM,WAAW,UAAU,eAAe,SAAS,aAAa,OAAO,CAAC,yBAAyB;gBACjG,cAAc;YAChB;QACF;6BAAG;QAAC;KAAS;IAEb,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,cAAc,aAAa,GAAG;gBAChC;YACF;QACF;6BAAG;QAAC;KAAW;IAEf,MAAM,WAAW;QACf,IAAI;YACF,WAAW;YACX,SAAS;YAET,QAAQ,GAAG,CAAC,+CAA+C;YAC3D,QAAQ,GAAG,CAAC,sBAAsB,CAAC,CAAC,aAAa,OAAO,CAAC;YAEzD,gCAAgC;YAChC,IAAI;gBACF,MAAM,OAAO,MAAM,oHAAA,CAAA,UAAO,CAAC,OAAO,CAAC;gBACnC,QAAQ,GAAG,CAAC,0CAA0C,MAAM,UAAU;gBAEtE,6BAA6B;gBAC7B,MAAM,aAAa,KAAK,MAAM,CAAC,CAAC,OAAe,CAAC,KAAK,KAAK;gBAC1D,MAAM,oBAAoB,KAAK,MAAM,CAAC,CAAC,OAAe,KAAK,KAAK;gBAEhE,QAAQ;gBACR,aAAa;gBAEb,sBAAsB;gBACtB,eAAe;YAEjB,EAAE,OAAO,UAAe;gBACtB,QAAQ,GAAG,CAAC;gBACZ,QAAQ,KAAK,CAAC,uBAAuB;gBAErC,4DAA4D;gBAC5D,IAAI;oBACF,MAAM,WAAW,MAAM,MAAM,CAAC,qCAAqC,EAAE,YAAY;oBACjF,MAAM,YAAY,MAAM,SAAS,IAAI;oBACrC,QAAQ,GAAG,CAAC,8BAA8B;oBAE1C,IAAI,UAAU,IAAI,IAAI,MAAM,OAAO,CAAC,UAAU,IAAI,GAAG;wBACnD,MAAM,aAAa,UAAU,IAAI,CAAC,MAAM,CAAC,CAAC,OAAc,CAAC,KAAK,KAAK;wBACnE,MAAM,oBAAoB,UAAU,IAAI,CAAC,MAAM,CAAC,CAAC,OAAc,KAAK,KAAK;wBAEzE,QAAQ;wBACR,aAAa;wBACb,eAAe;wBAEf,SAAS;oBACX,OAAO;wBACL,MAAM,IAAI,MAAM;oBAClB;gBACF,EAAE,OAAO,YAAY;oBACnB,QAAQ,KAAK,CAAC,mCAAmC;oBACjD,MAAM,SAAS,8BAA8B;;gBAC/C;YACF;QAEF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,2CAA2C;YACzD,SAAS,CAAC,iCAAiC,EAAE,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,EAAE;QAC9F,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,SAAS,SAAS,MAAM;QAC9B,MAAM,aAAa,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,YAAY,GAAG,GAAG,MAAM;QAClE,MAAM,YAAY,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,YAAY,KAAK,GAAG,MAAM,CAAC,gBAAgB;;QACpF,MAAM,cAAc,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,EAAE,MAAM;QAE9D,MAAM,cAAc,SAAS,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,aAAa,IAAI,CAAC,GAAG;QAC9E,MAAM,kBAAkB,SAAS,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,YAAY,IAAI,CAAC,GAAG;QACjF,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,YAAY,KAAK,GAAG,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,YAAY,IAAI,CAAC,GAAG;QAClH,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,YAAY,IAAI,CAAC,GAAG;QAE7G,SAAS;YACP;YACA;YACA;YACA;YACA,0BAA0B,SAAS,IAAI,KAAK,KAAK,CAAC,AAAC,aAAa,SAAU,OAAO;YACjF,yBAAyB,SAAS,IAAI,KAAK,KAAK,CAAC,AAAC,YAAY,SAAU,OAAO;YAC/E,2BAA2B,SAAS,IAAI,KAAK,KAAK,CAAC,AAAC,cAAc,SAAU,OAAO;YACnF;YACA;YACA;YACA;QACF;IACF;IAEA,2BAA2B;IAC3B,MAAM,qBAAqB,CAAC,MAAY;QACtC,QAAQ,GAAG,CAAC,kBAAkB,QAAQ,aAAa,KAAK,OAAO;QAE/D,OAAQ;YACN,KAAK;gBACH,wBAAwB;oBAAE,MAAM;oBAAM;gBAAK;gBAC3C;YACF,KAAK;gBACH,wBAAwB;oBAAE,MAAM;oBAAM;gBAAK;gBAC3C;QACJ;IACF;IAEA,MAAM,0BAA0B,CAAC,MAAY;QAC3C,QAAQ,GAAG,CAAC,wBAAwB,QAAQ,aAAa,KAAK,OAAO;QAErE,OAAQ;YACN,KAAK;gBACH,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBACA;YACF,KAAK;gBACH,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBACA;YACF,KAAK;gBACH,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBACA;YACF,KAAK;gBACH,MAAM;oBACJ,OAAO;oBACP,aAAa;oBACb,SAAS;gBACX;gBACA;QACJ;IACF;IAEA,oCAAoC;IACpC,MAAM,sBAAsB,CAAC;QAC3B,MAAM;YACJ,OAAO;YACP,aAAa;QACf;QACA,kBAAkB;QAClB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM;YACJ,OAAO;YACP,aAAa;YACb,SAAS;QACX;IACF;IAEA,IAAI,aAAa,SAAS;QACxB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,oNAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;;;;;;IAGzB;IAEA,IAAI,CAAC,YAAY;QACf,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,oIAAA,CAAA,QAAK;;sCACJ,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,6LAAC,oIAAA,CAAA,mBAAgB;sCAAC;;;;;;;;;;;;8BAIpB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAY;;;;;;sCAC1B,6LAAC;;gCAAE;gCAAO,OAAO,KAAK,QAAQ,GAAG;;;;;;;sCACjC,6LAAC;;gCAAE;gCAAmB,WAAW,SAAS,QAAQ,GAAG;;;;;;;sCACrD,6LAAC;;gCAAE;gCAAiB,uCAAiC,aAAa,OAAO,CAAC,WAAW,OAAO;;;;;;;;;;;;;;;;;;;IAIpG;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;;sCACb,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,6LAAC,oIAAA,CAAA,mBAAgB;sCAAE;;;;;;;;;;;;8BAErB,6LAAC,qIAAA,CAAA,SAAM;oBAAC,SAAS;oBAAU,WAAU;8BAAO;;;;;;;;;;;;IAKlD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAGb,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;0DAAqB,MAAM,MAAM;;;;;;0DAChD,6LAAC;gDAAI,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;0CAKnD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;0DAAqB,MAAM,UAAU;;;;;;0DACpD,6LAAC;gDAAI,WAAU;;oDAAgC;oDAChC,MAAM,wBAAwB;oDAAC;;;;;;;;;;;;;;;;;;;0CAMlD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;kDACf,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;0DAAqB,MAAM,SAAS;;;;;;0DACnD,6LAAC;gDAAI,WAAU;;oDAAgC;oDACjC,MAAM,uBAAuB;oDAAC;;;;;;;;;;;;;;;;;;;0CAMhD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;0DAAqB,MAAM,WAAW;;;;;;0DACrD,6LAAC;gDAAI,WAAU;;oDAAgC;oDAC/B,MAAM,yBAAyB;oDAAC;;;;;;;;;;;;;;;;;;;0CAMpD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;0DAAqB,MAAM,eAAe,CAAC,cAAc;;;;;;0DACxE,6LAAC;gDAAI,WAAU;;oDAAgC;oDACzC,MAAM,WAAW,CAAC,cAAc;oDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASnD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,0IAAA,CAAA,UAAS;oBACR,MAAM;oBACN,SAAS;oBACT,kBAAkB;oBAClB,cAAc;oBACd,mBAAmB;oBACnB,gBAAgB;oBAChB,qBAAqB;;;;;;;;;;;YAKxB,UAAU,MAAM,GAAG,mBAClB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,2MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,6LAAC;;4CAAK;4CAAa,UAAU,MAAM;4CAAC;;;;;;;;;;;;;;;;;;sCAGxC,6LAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC,0IAAA,CAAA,UAAS;gCACR,MAAM;gCACN,SAAS;gCACT,kBAAkB;gCAClB,gBAAgB;gCAChB,qBAAqB;;;;;;;;;;;;;;;;;;;;;;YAQ9B,oDAAyB,+BACxB,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,6LAAC,mIAAA,CAAA,cAAW;;0CACV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;;kEAAE,6LAAC;kEAAO;;;;;;oDAAc;oDAAE,OAAO,KAAK,QAAQ,GAAG;;;;;;;0DAClD,6LAAC;;kEAAE,6LAAC;kEAAO;;;;;;oDAAmB;oDAAE,OAAO,KAAK,KAAK,GAAG;;;;;;;0DACpD,6LAAC;;kEAAE,6LAAC;kEAAO;;;;;;oDAAqB;oDAAE;;;;;;;0DAClC,6LAAC;;kEAAE,6LAAC;kEAAO;;;;;;oDAA0B;oDAAE,WAAW,SAAS,QAAQ,GAAG;;;;;;;;;;;;;kDAExE,6LAAC;;0DACC,6LAAC;;kEAAE,6LAAC;kEAAO;;;;;;oDAAwB;oDAAE,uCAAiC,aAAa,OAAO,CAAC,WAAW,OAAO;;;;;;;0DAC7G,6LAAC;;kEAAE,6LAAC;kEAAO;;;;;;oDAAiB;oDAAE,UAAU,OAAO;;;;;;;0DAC/C,6LAAC;;kEAAE,6LAAC;kEAAO;;;;;;oDAAe;oDAAE,SAAS;;;;;;;0DACrC,6LAAC;;kEAAE,6LAAC;kEAAO;;;;;;oDAAuB;oDAAE,KAAK,MAAM;;;;;;;0DAC/C,6LAAC;;kEAAE,6LAAC;kEAAO;;;;;;oDAAoB;oDAAE,UAAU,MAAM;;;;;;;;;;;;;;;;;;;0CAGrD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS;wCACP,IAAI;4CACF,MAAM,WAAW,MAAM,MAAM;4CAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;4CAChC,QAAQ,GAAG,CAAC,4BAA4B;4CACxC,MAAM,CAAC,WAAW,EAAE,KAAK,UAAU,CAAC,oBAAoB,CAAC;wCAC3D,EAAE,OAAO,KAAK;4CACZ,QAAQ,KAAK,CAAC,0BAA0B;4CACxC,MAAM;wCACR;oCACF;oCACA,SAAQ;oCACR,MAAK;8CACN;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC,qJAAA,CAAA,UAAoB;gBACnB,MAAM,qBAAqB,IAAI;gBAC/B,SAAS,IAAM,wBAAwB;wBAAE,MAAM;wBAAO,MAAM;oBAAK;gBACjE,MAAM,qBAAqB,IAAI;gBAC/B,WAAW;gBACX,SAAS;;;;;;0BAGX,6LAAC,qJAAA,CAAA,UAAoB;gBACnB,MAAM,qBAAqB,IAAI;gBAC/B,SAAS,IAAM,wBAAwB;wBAAE,MAAM;wBAAO,MAAM;oBAAK;gBACjE,MAAM,qBAAqB,IAAI;gBAC/B,WAAW;gBACX,SAAS;;;;;;;;;;;;AAIjB;GA1ZwB;;QACiC,kIAAA,CAAA,UAAO;QAC/C,qIAAA,CAAA,YAAS;QACN,+HAAA,CAAA,WAAQ;;;KAHJ", "debugId": null}}]}