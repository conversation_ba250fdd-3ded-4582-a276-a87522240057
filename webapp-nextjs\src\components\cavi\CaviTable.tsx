'use client'

import { useState, useMemo, useEffect } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Cavo } from '@/types'
import CaviFilters from './CaviFilters'
import {
  MoreHorizontal,
  Cable,
  Settings,
  Zap,
  CheckCircle,
  AlertCircle,
  Clock,
  Package
} from 'lucide-react'

interface CaviTableProps {
  cavi: Cavo[]
  loading?: boolean
  selectionEnabled?: boolean
  selectedCavi?: number[]
  onSelectionChange?: (selectedIds: number[]) => void
  onStatusAction?: (cavo: Cavo, action: string) => void
  onContextMenuAction?: (cavo: Cavo, action: string) => void
}

export default function CaviTable({
  cavi = [],
  loading = false,
  selectionEnabled = false,
  selectedCavi = [],
  onSelectionChange,
  onStatusAction,
  onContextMenuAction
}: CaviTableProps) {
  const [filteredCavi, setFilteredCavi] = useState(cavi)
  const [internalSelectionEnabled, setInternalSelectionEnabled] = useState(selectionEnabled)

  // Aggiorna i cavi filtrati quando cambiano i cavi originali
  useEffect(() => {
    setFilteredCavi(cavi)
  }, [cavi])

  // Gestione filtri
  const handleFilterChange = (filtered: Cavo[]) => {
    setFilteredCavi(filtered)
  }

  const handleSelectionToggle = () => {
    setInternalSelectionEnabled(!internalSelectionEnabled)
  }

  // Gestione selezione
  const handleSelectAll = (checked: boolean) => {
    if (onSelectionChange) {
      onSelectionChange(checked ? filteredCavi.map(c => c.id_cavo) : [])
    }
  }

  const handleSelectCavo = (cavoId: number, checked: boolean) => {
    if (onSelectionChange) {
      const newSelection = checked
        ? [...selectedCavi, cavoId]
        : selectedCavi.filter(id => id !== cavoId)
      onSelectionChange(newSelection)
    }
  }

  // Funzioni di utilità per lo stato
  const getStatusBadge = (cavo: Cavo) => {
    const isInstalled = cavo.metri_posati > 0
    const isConnected = cavo.collegamento === 3
    const isCertified = cavo.certificato

    if (isCertified) {
      return <Badge className="bg-green-100 text-green-800">Certificato</Badge>
    } else if (isConnected) {
      return <Badge className="bg-blue-100 text-blue-800">Collegato</Badge>
    } else if (isInstalled) {
      return <Badge className="bg-yellow-100 text-yellow-800">Installato</Badge>
    } else {
      return <Badge variant="outline">Da Installare</Badge>
    }
  }

  const getStatusButton = (cavo: Cavo) => {
    const isInstalled = cavo.metri_posati > 0

    if (!isInstalled) {
      return (
        <Button
          size="sm"
          variant="outline"
          onClick={() => onStatusAction?.(cavo, 'insert_meters')}
          className="text-xs"
        >
          <Package className="h-3 w-3 mr-1" />
          Inserisci Metri
        </Button>
      )
    } else {
      return (
        <Button
          size="sm"
          variant="outline"
          onClick={() => onStatusAction?.(cavo, 'modify_reel')}
          className="text-xs"
        >
          <Settings className="h-3 w-3 mr-1" />
          Modifica Bobina
        </Button>
      )
    }
  }

  const getConnectionIndicator = (collegamento: number) => {
    switch (collegamento) {
      case 1:
        return <div className="w-3 h-3 rounded-full bg-red-500" title="Lato partenza" />
      case 2:
        return <div className="w-3 h-3 rounded-full bg-yellow-500" title="Lato arrivo" />
      case 3:
        return <div className="w-3 h-3 rounded-full bg-green-500" title="Collegato" />
      default:
        return <div className="w-3 h-3 rounded-full bg-gray-300" title="Non collegato" />
    }
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">Caricamento cavi...</div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div>
      {/* Filtri intelligenti */}
      <CaviFilters
        cavi={cavi}
        onFilterChange={handleFilterChange}
        onSelectionToggle={handleSelectionToggle}
        selectionEnabled={internalSelectionEnabled}
      />

      {/* Tabella */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <Cable className="h-5 w-5" />
              <span>Elenco Cavi ({filteredCavi.length})</span>
            </CardTitle>

            {internalSelectionEnabled && selectedCavi.length > 0 && (
              <Badge variant="secondary">
                {selectedCavi.length} selezionati
              </Badge>
            )}
          </div>
        </CardHeader>

      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              {internalSelectionEnabled && (
                <TableHead className="w-12">
                  <Checkbox
                    checked={selectedCavi.length === filteredCavi.length && filteredCavi.length > 0}
                    onCheckedChange={handleSelectAll}
                  />
                </TableHead>
              )}
              <TableHead>ID Cavo</TableHead>
              <TableHead>Sistema</TableHead>
              <TableHead>Utility</TableHead>
              <TableHead>Tipologia</TableHead>
              <TableHead>Formazione</TableHead>
              <TableHead>Da</TableHead>
              <TableHead>A</TableHead>
              <TableHead>Metri Teorici</TableHead>
              <TableHead>Metri Posati</TableHead>
              <TableHead>Bobina</TableHead>
              <TableHead>Collegamento</TableHead>
              <TableHead>Stato</TableHead>
              <TableHead>Azioni</TableHead>
              <TableHead className="w-12"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredCavi.map((cavo) => (
              <TableRow key={cavo.id_cavo}>
                {internalSelectionEnabled && (
                  <TableCell>
                    <Checkbox
                      checked={selectedCavi.includes(cavo.id_cavo)}
                      onCheckedChange={(checked) => handleSelectCavo(cavo.id_cavo, checked as boolean)}
                    />
                  </TableCell>
                )}
                <TableCell className="font-medium">{cavo.id_cavo}</TableCell>
                <TableCell>{cavo.sistema}</TableCell>
                <TableCell>{cavo.utility}</TableCell>
                <TableCell>{cavo.tipologia}</TableCell>
                <TableCell>{cavo.formazione}</TableCell>
                <TableCell>{cavo.da}</TableCell>
                <TableCell>{cavo.a}</TableCell>
                <TableCell>{cavo.metri_teorici}</TableCell>
                <TableCell>{cavo.metri_posati || 0}</TableCell>
                <TableCell>{cavo.id_bobina || 'N/A'}</TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    {getConnectionIndicator(cavo.collegamento)}
                    <span className="text-xs">
                      {cavo.collegamento === 1 ? 'Partenza' :
                       cavo.collegamento === 2 ? 'Arrivo' :
                       cavo.collegamento === 3 ? 'Collegato' : 'Non collegato'}
                    </span>
                  </div>
                </TableCell>
                <TableCell>{getStatusBadge(cavo)}</TableCell>
                <TableCell>{getStatusButton(cavo)}</TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => onContextMenuAction?.(cavo, 'edit')}>
                        Modifica Cavo
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => onContextMenuAction?.(cavo, 'connections')}>
                        Collegamenti
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => onContextMenuAction?.(cavo, 'certification')}>
                        Certificazione
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => onContextMenuAction?.(cavo, 'delete')} className="text-red-600">
                        Elimina Cavo
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>

        {filteredCavi.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            Nessun cavo trovato con i filtri selezionati
          </div>
        )}
      </CardContent>
    </Card>
    </div>
  )
}
