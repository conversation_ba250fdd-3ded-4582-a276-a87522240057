'use client'

import { useState, useMemo, useEffect } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Cavo } from '@/types'
import CaviFilters from './CaviFilters'
import {
  MoreHorizontal,
  Cable,
  Settings,
  Zap,
  CheckCircle,
  AlertCircle,
  Clock,
  Package
} from 'lucide-react'

interface CaviTableProps {
  cavi: Cavo[]
  loading?: boolean
  selectionEnabled?: boolean
  selectedCavi?: string[]
  onSelectionChange?: (selectedIds: string[]) => void
  onStatusAction?: (cavo: Cavo, action: string) => void
  onContextMenuAction?: (cavo: Cavo, action: string) => void
}

export default function CaviTable({
  cavi = [],
  loading = false,
  selectionEnabled = false,
  selectedCavi = [],
  onSelectionChange,
  onStatusAction,
  onContextMenuAction
}: CaviTableProps) {
  const [filteredCavi, setFilteredCavi] = useState(cavi)
  const [internalSelectionEnabled, setInternalSelectionEnabled] = useState(selectionEnabled)

  // Aggiorna i cavi filtrati quando cambiano i cavi originali
  useEffect(() => {
    setFilteredCavi(cavi)
  }, [cavi])

  // Gestione filtri
  const handleFilterChange = (filtered: Cavo[]) => {
    setFilteredCavi(filtered)
  }

  const handleSelectionToggle = () => {
    setInternalSelectionEnabled(!internalSelectionEnabled)
  }

  // Gestione selezione
  const handleSelectAll = (checked: boolean) => {
    if (onSelectionChange) {
      onSelectionChange(checked ? filteredCavi.map(c => c.id_cavo) : [])
    }
  }

  const handleSelectCavo = (cavoId: string, checked: boolean) => {
    if (onSelectionChange) {
      const newSelection = checked
        ? [...selectedCavi, cavoId]
        : selectedCavi.filter(id => id !== cavoId)
      onSelectionChange(newSelection)
    }
  }

  // Funzioni di utilità per lo stato
  const getStatusBadge = (cavo: Cavo) => {
    // Verifica se il cavo è assegnato a una comanda
    const comandaPosa = cavo.comanda_posa
    const comandaPartenza = cavo.comanda_partenza
    const comandaArrivo = cavo.comanda_arrivo
    const comandaCertificazione = cavo.comanda_certificazione

    // Trova la comanda attiva (priorità: posa > partenza > arrivo > certificazione)
    const comandaAttiva = comandaPosa || comandaPartenza || comandaArrivo || comandaCertificazione

    // Se c'è una comanda attiva e lo stato è "In corso", mostra il codice comanda
    if (comandaAttiva && cavo.stato_installazione === 'In corso') {
      return (
        <Badge
          className="bg-blue-600 text-white cursor-pointer hover:bg-blue-700"
          onClick={() => onStatusAction?.(cavo, 'view_command', comandaAttiva)}
        >
          {comandaAttiva}
        </Badge>
      )
    }

    // Logica normale per gli altri stati
    const stato = cavo.stato_installazione || 'Da installare'

    switch (stato) {
      case 'Installato':
        return <Badge className="bg-green-100 text-green-800">Installato</Badge>
      case 'In corso':
        return <Badge className="bg-yellow-100 text-yellow-800">In corso</Badge>
      case 'Da installare':
        return <Badge variant="outline">Da installare</Badge>
      default:
        return <Badge variant="outline">{stato}</Badge>
    }
  }

  const getStatusButton = (cavo: Cavo) => {
    const isInstalled = cavo.metri_posati > 0

    if (!isInstalled) {
      return (
        <Button
          size="sm"
          variant="outline"
          onClick={() => onStatusAction?.(cavo, 'insert_meters')}
          className="text-xs"
        >
          <Package className="h-3 w-3 mr-1" />
          Inserisci Metri
        </Button>
      )
    } else {
      return (
        <Button
          size="sm"
          variant="outline"
          onClick={() => onStatusAction?.(cavo, 'modify_reel')}
          className="text-xs"
        >
          <Settings className="h-3 w-3 mr-1" />
          Modifica Bobina
        </Button>
      )
    }
  }

  const getConnectionButton = (cavo: Cavo) => {
    const isInstalled = cavo.metri_posati > 0 || cavo.metratura_reale > 0
    const collegamento = cavo.collegamento || cavo.collegamenti || 0

    if (!isInstalled) {
      return (
        <Button
          size="sm"
          variant="outline"
          disabled
          className="text-xs"
        >
          <AlertCircle className="h-3 w-3 mr-1" />
          Non disponibile
        </Button>
      )
    }

    let label, actionType, variant: "default" | "outline" | "secondary" | "destructive" | "ghost" | "link" = "outline"
    let icon

    switch (collegamento) {
      case 0:
        label = "⚪⚪ Collega cavo"
        actionType = "connect_cable"
        icon = <Zap className="h-3 w-3 mr-1" />
        break
      case 1:
        label = "🟢⚪ Completa collegamento"
        actionType = "connect_arrival"
        icon = <Zap className="h-3 w-3 mr-1" />
        variant = "secondary"
        break
      case 2:
        label = "⚪🟢 Completa collegamento"
        actionType = "connect_departure"
        icon = <Zap className="h-3 w-3 mr-1" />
        variant = "secondary"
        break
      case 3:
        label = "🟢🟢 Scollega cavo"
        actionType = "disconnect_cable"
        icon = <CheckCircle className="h-3 w-3 mr-1" />
        variant = "default"
        break
      default:
        label = "Gestisci collegamenti"
        actionType = "manage_connections"
        icon = <Settings className="h-3 w-3 mr-1" />
        break
    }

    return (
      <Button
        size="sm"
        variant={variant}
        onClick={() => onStatusAction?.(cavo, actionType)}
        className="text-xs"
      >
        {icon}
        {label}
      </Button>
    )
  }

  const getCertificationButton = (cavo: Cavo) => {
    const isInstalled = cavo.metri_posati > 0 || cavo.metratura_reale > 0
    const isCertified = cavo.certificato === true || cavo.certificato === 'SI' || cavo.certificato === 'CERTIFICATO'

    if (!isInstalled) {
      return (
        <Button
          size="sm"
          variant="outline"
          disabled
          className="text-xs"
        >
          <AlertCircle className="h-3 w-3 mr-1" />
          Non disponibile
        </Button>
      )
    }

    if (isCertified) {
      return (
        <Button
          size="sm"
          variant="default"
          onClick={() => onStatusAction?.(cavo, 'generate_pdf')}
          className="text-xs bg-green-600 hover:bg-green-700"
        >
          <CheckCircle className="h-3 w-3 mr-1" />
          Genera PDF
        </Button>
      )
    }

    return (
      <Button
        size="sm"
        variant="outline"
        onClick={() => onStatusAction?.(cavo, 'create_certificate')}
        className="text-xs"
      >
        <Clock className="h-3 w-3 mr-1" />
        Certifica cavo
      </Button>
    )
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">Caricamento cavi...</div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div>
      {/* Filtri intelligenti */}
      <CaviFilters
        cavi={cavi}
        onFilterChange={handleFilterChange}
        onSelectionToggle={handleSelectionToggle}
        selectionEnabled={internalSelectionEnabled}
      />

      {/* Tabella */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <Cable className="h-5 w-5" />
              <span>Elenco Cavi ({filteredCavi.length})</span>
            </CardTitle>

            {internalSelectionEnabled && selectedCavi.length > 0 && (
              <Badge variant="secondary">
                {selectedCavi.length} selezionati
              </Badge>
            )}
          </div>
        </CardHeader>

      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              {internalSelectionEnabled && (
                <TableHead className="w-12">
                  <Checkbox
                    checked={selectedCavi.length === filteredCavi.length && filteredCavi.length > 0}
                    onCheckedChange={handleSelectAll}
                  />
                </TableHead>
              )}
              <TableHead>ID Cavo</TableHead>
              <TableHead>Sistema</TableHead>
              <TableHead>Utility</TableHead>
              <TableHead>Tipologia</TableHead>
              <TableHead>Formazione</TableHead>
              <TableHead>Da</TableHead>
              <TableHead>A</TableHead>
              <TableHead>Metri Teorici</TableHead>
              <TableHead>Metri Posati</TableHead>
              <TableHead>Bobina</TableHead>
              <TableHead>Stato</TableHead>
              <TableHead>Collegamenti</TableHead>
              <TableHead>Certificato</TableHead>
              <TableHead>Azioni</TableHead>
              <TableHead className="w-12"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredCavi.map((cavo) => (
              <TableRow
                key={cavo.id_cavo}
                className={`${selectedCavi.includes(cavo.id_cavo) ? 'bg-blue-50' : ''} hover:bg-gray-50 cursor-pointer`}
                onClick={() => internalSelectionEnabled && handleSelectCavo(cavo.id_cavo, !selectedCavi.includes(cavo.id_cavo))}
                onContextMenu={(e) => {
                  e.preventDefault()
                  onContextMenuAction?.(cavo, 'context_menu')
                }}
              >
                {internalSelectionEnabled && (
                  <TableCell>
                    <Checkbox
                      checked={selectedCavi.includes(cavo.id_cavo)}
                      onCheckedChange={(checked) => handleSelectCavo(cavo.id_cavo, checked as boolean)}
                      onClick={(e) => e.stopPropagation()}
                    />
                  </TableCell>
                )}
                <TableCell className="font-medium">{cavo.id_cavo}</TableCell>
                <TableCell>{cavo.sistema}</TableCell>
                <TableCell>{cavo.utility}</TableCell>
                <TableCell>{cavo.tipologia}</TableCell>
                <TableCell>{cavo.formazione || cavo.sezione}</TableCell>
                <TableCell>{cavo.da || cavo.ubicazione_partenza}</TableCell>
                <TableCell>{cavo.a || cavo.ubicazione_arrivo}</TableCell>
                <TableCell>{cavo.metri_teorici}</TableCell>
                <TableCell>{cavo.metri_posati || cavo.metratura_reale || 0}</TableCell>
                <TableCell>{cavo.id_bobina || 'N/A'}</TableCell>
                <TableCell onClick={(e) => e.stopPropagation()}>{getStatusBadge(cavo)}</TableCell>
                <TableCell onClick={(e) => e.stopPropagation()}>{getConnectionButton(cavo)}</TableCell>
                <TableCell onClick={(e) => e.stopPropagation()}>{getCertificationButton(cavo)}</TableCell>
                <TableCell onClick={(e) => e.stopPropagation()}>{getStatusButton(cavo)}</TableCell>
                <TableCell onClick={(e) => e.stopPropagation()}>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onContextMenuAction?.(cavo, 'menu')}
                  >
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>

        {filteredCavi.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            Nessun cavo trovato con i filtri selezionati
          </div>
        )}
      </CardContent>
    </Card>
    </div>
  )
}
